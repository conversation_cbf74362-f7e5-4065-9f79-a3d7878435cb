{"version": 3, "file": "index.js", "sources": ["../src/shouldStoreRHSInTemporaryVariable.ts", "../src/compat-data.ts", "../src/index.ts"], "sourcesContent": ["import type { types as t } from \"@babel/core\";\n\n/**\n * This is a helper function to determine if we should create an intermediate variable\n * such that the RHS of an assignment is not duplicated.\n *\n * See https://github.com/babel/babel/pull/13711#issuecomment-914388382 for discussion\n * on further optimizations.\n */\nexport default function shouldStoreRHSInTemporaryVariable(\n  node: t.LVal,\n): boolean {\n  if (!node) return false;\n  if (node.type === \"ArrayPattern\") {\n    const nonNullElements = node.elements.filter(element => element !== null);\n    if (nonNullElements.length > 1) return true;\n    else return shouldStoreRHSInTemporaryVariable(nonNullElements[0]);\n  } else if (node.type === \"ObjectPattern\") {\n    const { properties } = node;\n    if (properties.length > 1) return true;\n    else if (properties.length === 0) return false;\n    else {\n      const firstProperty = properties[0];\n      if (firstProperty.type === \"ObjectProperty\") {\n        // the value of the property must be an LVal\n        return shouldStoreRHSInTemporaryVariable(firstProperty.value as t.LVal);\n      } else {\n        return shouldStoreRHSInTemporaryVariable(firstProperty);\n      }\n    }\n  } else if (node.type === \"AssignmentPattern\") {\n    return shouldStoreRHSInTemporaryVariable(node.left);\n  } else if (node.type === \"RestElement\") {\n    if (node.argument.type === \"Identifier\") return true;\n    return shouldStoreRHSInTemporaryVariable(node.argument);\n  } else {\n    // node is Identifier or MemberExpression\n    return false;\n  }\n}\n", "export default {\n  \"Object.assign\": {\n    chrome: \"49\",\n    opera: \"36\",\n    edge: \"13\",\n    firefox: \"36\",\n    safari: \"10\",\n    node: \"6\",\n    deno: \"1\",\n    ios: \"10\",\n    samsung: \"5\",\n    opera_mobile: \"36\",\n    electron: \"0.37\",\n  },\n};\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport type { PluginPass, NodePath, Scope } from \"@babel/core\";\nimport { convertFunctionParams } from \"@babel/plugin-transform-parameters\";\nimport { isRequired } from \"@babel/helper-compilation-targets\";\nimport shouldStoreRHSInTemporaryVariable from \"./shouldStoreRHSInTemporaryVariable.ts\";\nimport compatData from \"./compat-data.ts\";\n\nconst { isAssignmentPattern, isObjectProperty } = t;\n// @babel/types <=7.3.3 counts FOO as referenced in var { x: FOO }.\n// We need to detect this bug to know if \"unused\" means 0 or 1 references.\nif (!process.env.BABEL_8_BREAKING) {\n  const node = t.identifier(\"a\");\n  const property = t.objectProperty(t.identifier(\"key\"), node);\n  const pattern = t.objectPattern([property]);\n\n  // eslint-disable-next-line no-var\n  var ZERO_REFS = t.isReferenced(node, property, pattern) ? 1 : 0;\n}\n\ntype Param = NodePath<t.Function[\"params\"][number]>;\nexport interface Options {\n  useBuiltIns?: boolean;\n  loose?: boolean;\n}\n\nexport default declare((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const targets = api.targets();\n  const supportsObjectAssign = !isRequired(\"Object.assign\", targets, {\n    compatData,\n  });\n\n  const { useBuiltIns = supportsObjectAssign, loose = false } = opts;\n\n  if (typeof loose !== \"boolean\") {\n    throw new Error(\".loose must be a boolean, or undefined\");\n  }\n\n  const ignoreFunctionLength = api.assumption(\"ignoreFunctionLength\") ?? loose;\n  const objectRestNoSymbols = api.assumption(\"objectRestNoSymbols\") ?? loose;\n  const pureGetters = api.assumption(\"pureGetters\") ?? loose;\n  const setSpreadProperties = api.assumption(\"setSpreadProperties\") ?? loose;\n\n  function getExtendsHelper(\n    file: PluginPass,\n  ): t.MemberExpression | t.Identifier {\n    return useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : file.addHelper(\"extends\");\n  }\n\n  function hasRestElement(path: Param) {\n    let foundRestElement = false;\n    visitRestElements(path, restElement => {\n      foundRestElement = true;\n      restElement.stop();\n    });\n    return foundRestElement;\n  }\n\n  function hasObjectPatternRestElement(path: NodePath): boolean {\n    let foundRestElement = false;\n    visitRestElements(path, restElement => {\n      if (restElement.parentPath.isObjectPattern()) {\n        foundRestElement = true;\n        restElement.stop();\n      }\n    });\n    return foundRestElement;\n  }\n\n  function visitRestElements(\n    path: NodePath,\n    visitor: (path: NodePath<t.RestElement>) => any,\n  ) {\n    path.traverse({\n      Expression(path) {\n        const { parent, key } = path;\n        if (\n          (isAssignmentPattern(parent) && key === \"right\") ||\n          (isObjectProperty(parent) && parent.computed && key === \"key\")\n        ) {\n          path.skip();\n        }\n      },\n      RestElement: visitor,\n    });\n  }\n\n  function hasSpread(node: t.ObjectExpression): boolean {\n    for (const prop of node.properties) {\n      if (t.isSpreadElement(prop)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // returns an array of all keys of an object, and a status flag indicating if all extracted keys\n  // were converted to stringLiterals or not\n  // e.g. extracts {keys: [\"a\", \"b\", \"3\", ++x], allPrimitives: false }\n  // from ast of {a: \"foo\", b, 3: \"bar\", [++x]: \"baz\"}\n  // `allPrimitives: false` doesn't necessarily mean that there is a non-primitive, but just\n  // that we are not sure.\n  function extractNormalizedKeys(node: t.ObjectPattern) {\n    // RestElement has been removed in createObjectRest\n    const props = node.properties as t.ObjectProperty[];\n    const keys: t.Expression[] = [];\n    let allPrimitives = true;\n    let hasTemplateLiteral = false;\n\n    for (const prop of props) {\n      const { key } = prop;\n      if (t.isIdentifier(key) && !prop.computed) {\n        // since a key {a: 3} is equivalent to {\"a\": 3}, use the latter\n        keys.push(t.stringLiteral(key.name));\n      } else if (t.isTemplateLiteral(key)) {\n        keys.push(t.cloneNode(key));\n        hasTemplateLiteral = true;\n      } else if (t.isLiteral(key)) {\n        keys.push(\n          t.stringLiteral(\n            String(\n              // @ts-expect-error prop.key can not be a NullLiteral\n              key.value,\n            ),\n          ),\n        );\n      } else {\n        // @ts-expect-error private name has been handled by destructuring-private\n        keys.push(t.cloneNode(key));\n\n        if (\n          (t.isMemberExpression(key, { computed: false }) &&\n            t.isIdentifier(key.object, { name: \"Symbol\" })) ||\n          (t.isCallExpression(key) &&\n            t.matchesPattern(key.callee, \"Symbol.for\"))\n        ) {\n          // there all return a primitive\n        } else {\n          allPrimitives = false;\n        }\n      }\n    }\n\n    return { keys, allPrimitives, hasTemplateLiteral };\n  }\n\n  // replaces impure computed keys with new identifiers\n  // and returns variable declarators of these new identifiers\n  function replaceImpureComputedKeys(\n    properties: NodePath<t.ObjectProperty>[],\n    scope: Scope,\n  ) {\n    const impureComputedPropertyDeclarators: t.VariableDeclarator[] = [];\n    for (const propPath of properties) {\n      // PrivateName is handled in destructuring-private plugin\n      const key = propPath.get(\"key\") as NodePath<t.Expression>;\n      if (propPath.node.computed && !key.isPure()) {\n        const name = scope.generateUidBasedOnNode(key.node);\n        const declarator = t.variableDeclarator(t.identifier(name), key.node);\n        impureComputedPropertyDeclarators.push(declarator);\n        key.replaceWith(t.identifier(name));\n      }\n    }\n    return impureComputedPropertyDeclarators;\n  }\n\n  function removeUnusedExcludedKeys(path: NodePath<t.ObjectPattern>): void {\n    const bindings = path.getOuterBindingIdentifierPaths();\n\n    Object.keys(bindings).forEach(bindingName => {\n      const bindingParentPath = bindings[bindingName].parentPath;\n      if (\n        path.scope.getBinding(bindingName).references >\n          (process.env.BABEL_8_BREAKING ? 0 : ZERO_REFS) ||\n        !bindingParentPath.isObjectProperty()\n      ) {\n        return;\n      }\n      bindingParentPath.remove();\n    });\n  }\n\n  //expects path to an object pattern\n  function createObjectRest(\n    path: NodePath<t.ObjectPattern>,\n    file: PluginPass,\n    objRef: t.Identifier | t.MemberExpression,\n  ): [\n    t.VariableDeclarator[],\n    t.AssignmentExpression[\"left\"],\n    t.CallExpression,\n  ] {\n    const props = path.get(\"properties\");\n    const last = props[props.length - 1];\n    t.assertRestElement(last.node);\n    const restElement = t.cloneNode(last.node);\n    last.remove();\n\n    const impureComputedPropertyDeclarators = replaceImpureComputedKeys(\n      path.get(\"properties\") as NodePath<t.ObjectProperty>[],\n      path.scope,\n    );\n    const { keys, allPrimitives, hasTemplateLiteral } = extractNormalizedKeys(\n      path.node,\n    );\n\n    if (keys.length === 0) {\n      return [\n        impureComputedPropertyDeclarators,\n        restElement.argument,\n        t.callExpression(getExtendsHelper(file), [\n          t.objectExpression([]),\n          t.sequenceExpression([\n            t.callExpression(file.addHelper(\"objectDestructuringEmpty\"), [\n              t.cloneNode(objRef),\n            ]),\n            t.cloneNode(objRef),\n          ]),\n        ]),\n      ];\n    }\n\n    let keyExpression;\n    if (!allPrimitives) {\n      // map to toPropertyKey to handle the possible non-string values\n      keyExpression = t.callExpression(\n        t.memberExpression(t.arrayExpression(keys), t.identifier(\"map\")),\n        [file.addHelper(\"toPropertyKey\")],\n      );\n    } else {\n      keyExpression = t.arrayExpression(keys);\n\n      if (!hasTemplateLiteral && !t.isProgram(path.scope.block)) {\n        // Hoist definition of excluded keys, so that it's not created each time.\n        const program = path.findParent(path => path.isProgram());\n        const id = path.scope.generateUidIdentifier(\"excluded\");\n\n        program.scope.push({\n          id,\n          init: keyExpression,\n          kind: \"const\",\n        });\n\n        keyExpression = t.cloneNode(id);\n      }\n    }\n\n    return [\n      impureComputedPropertyDeclarators,\n      restElement.argument,\n      t.callExpression(\n        file.addHelper(\n          `objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`,\n        ),\n        [t.cloneNode(objRef), keyExpression],\n      ),\n    ];\n  }\n\n  function replaceRestElement(\n    parentPath: NodePath<t.Function | t.CatchClause>,\n    paramPath: NodePath<\n      t.Function[\"params\"][number] | t.AssignmentPattern[\"left\"]\n    >,\n    container?: t.VariableDeclaration[],\n  ): void {\n    if (paramPath.isAssignmentPattern()) {\n      replaceRestElement(parentPath, paramPath.get(\"left\"), container);\n      return;\n    }\n\n    if (paramPath.isArrayPattern() && hasRestElement(paramPath)) {\n      const elements = paramPath.get(\"elements\");\n\n      for (let i = 0; i < elements.length; i++) {\n        replaceRestElement(parentPath, elements[i], container);\n      }\n    }\n\n    if (paramPath.isObjectPattern() && hasRestElement(paramPath)) {\n      const uid = parentPath.scope.generateUidIdentifier(\"ref\");\n\n      const declar = t.variableDeclaration(\"let\", [\n        t.variableDeclarator(paramPath.node, uid),\n      ]);\n\n      if (container) {\n        container.push(declar);\n      } else {\n        parentPath.ensureBlock();\n        (parentPath.get(\"body\") as NodePath<t.BlockStatement>).unshiftContainer(\n          \"body\",\n          declar,\n        );\n      }\n      paramPath.replaceWith(t.cloneNode(uid));\n    }\n  }\n\n  return {\n    name: \"transform-object-rest-spread\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"objectRestSpread\"),\n\n    visitor: {\n      // function a({ b, ...c }) {}\n      Function(path) {\n        const params = path.get(\"params\");\n        const paramsWithRestElement = new Set<number>();\n        const idsInRestParams = new Set();\n        for (let i = 0; i < params.length; ++i) {\n          const param = params[i];\n          if (hasRestElement(param)) {\n            paramsWithRestElement.add(i);\n            for (const name of Object.keys(param.getBindingIdentifiers())) {\n              idsInRestParams.add(name);\n            }\n          }\n        }\n\n        // if true, a parameter exists that has an id in its initializer\n        // that is also an id bound in a rest parameter\n        // example: f({...R}, a = R)\n        let idInRest = false;\n\n        const IdentifierHandler = function (\n          path: NodePath<t.Identifier>,\n          functionScope: Scope,\n        ) {\n          const name = path.node.name;\n          if (\n            path.scope.getBinding(name) === functionScope.getBinding(name) &&\n            idsInRestParams.has(name)\n          ) {\n            idInRest = true;\n            path.stop();\n          }\n        };\n\n        let i: number;\n        for (i = 0; i < params.length && !idInRest; ++i) {\n          const param = params[i];\n          if (!paramsWithRestElement.has(i)) {\n            if (param.isReferencedIdentifier() || param.isBindingIdentifier()) {\n              IdentifierHandler(param, path.scope);\n            } else {\n              param.traverse(\n                {\n                  \"Scope|TypeAnnotation|TSTypeAnnotation\": path => path.skip(),\n                  \"ReferencedIdentifier|BindingIdentifier\": IdentifierHandler,\n                },\n                path.scope,\n              );\n            }\n          }\n        }\n\n        if (!idInRest) {\n          for (let i = 0; i < params.length; ++i) {\n            const param = params[i];\n            if (paramsWithRestElement.has(i)) {\n              replaceRestElement(path, param);\n            }\n          }\n        } else {\n          const shouldTransformParam = (idx: number) =>\n            idx >= i - 1 || paramsWithRestElement.has(idx);\n          convertFunctionParams(\n            path,\n            ignoreFunctionLength,\n            shouldTransformParam,\n            replaceRestElement,\n          );\n        }\n      },\n\n      // adapted from transform-destructuring/src/index.js#pushObjectRest\n      // const { a, ...b } = c;\n      VariableDeclarator(path, file) {\n        if (!path.get(\"id\").isObjectPattern()) {\n          return;\n        }\n\n        let insertionPath = path;\n        const originalPath = path;\n\n        visitRestElements(path.get(\"id\"), path => {\n          if (!path.parentPath.isObjectPattern()) {\n            // Return early if the parent is not an ObjectPattern, but\n            // (for example) an ArrayPattern or Function, because that\n            // means this RestElement is an not an object property.\n            return;\n          }\n\n          if (\n            // skip single-property case, e.g.\n            // const { ...x } = foo();\n            // since the RHS will not be duplicated\n            shouldStoreRHSInTemporaryVariable(originalPath.node.id) &&\n            !t.isIdentifier(originalPath.node.init)\n          ) {\n            // const { a, ...b } = foo();\n            // to avoid calling foo() twice, as a first step convert it to:\n            // const _foo = foo(),\n            //       { a, ...b } = _foo;\n            const initRef = path.scope.generateUidIdentifierBasedOnNode(\n              originalPath.node.init,\n              \"ref\",\n            );\n            // insert _foo = foo()\n            originalPath.insertBefore(\n              t.variableDeclarator(initRef, originalPath.node.init),\n            );\n            // replace foo() with _foo\n            originalPath.replaceWith(\n              t.variableDeclarator(originalPath.node.id, t.cloneNode(initRef)),\n            );\n\n            return;\n          }\n\n          let ref = originalPath.node.init;\n          const refPropertyPath: NodePath<t.ObjectProperty>[] = [];\n          let kind;\n\n          path.findParent((path: NodePath): boolean => {\n            if (path.isObjectProperty()) {\n              refPropertyPath.unshift(path);\n            } else if (path.isVariableDeclarator()) {\n              kind = path.parentPath.node.kind;\n              return true;\n            }\n          });\n\n          const impureObjRefComputedDeclarators = replaceImpureComputedKeys(\n            refPropertyPath,\n            path.scope,\n          );\n          refPropertyPath.forEach(prop => {\n            const { node } = prop;\n            ref = t.memberExpression(\n              ref,\n              t.cloneNode(node.key),\n              node.computed || t.isLiteral(node.key),\n            );\n          });\n\n          //@ts-expect-error: findParent can not apply assertions on result shape\n          const objectPatternPath: NodePath<t.ObjectPattern> = path.findParent(\n            path => path.isObjectPattern(),\n          );\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(\n              objectPatternPath,\n              file,\n              ref as t.MemberExpression,\n            );\n\n          if (pureGetters) {\n            removeUnusedExcludedKeys(objectPatternPath);\n          }\n\n          t.assertIdentifier(argument);\n\n          insertionPath.insertBefore(impureComputedPropertyDeclarators);\n\n          insertionPath.insertBefore(impureObjRefComputedDeclarators);\n\n          insertionPath = insertionPath.insertAfter(\n            t.variableDeclarator(argument, callExpression),\n          )[0] as NodePath<t.VariableDeclarator>;\n\n          path.scope.registerBinding(kind, insertionPath);\n\n          if (objectPatternPath.node.properties.length === 0) {\n            objectPatternPath\n              .findParent(\n                path => path.isObjectProperty() || path.isVariableDeclarator(),\n              )\n              .remove();\n          }\n        });\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      // export var { a, ...b } = c;\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n\n        const hasRest = declaration\n          .get(\"declarations\")\n          .some(path => hasObjectPatternRestElement(path.get(\"id\")));\n        if (!hasRest) return;\n\n        const specifiers = [];\n\n        for (const name of Object.keys(path.getOuterBindingIdentifiers(true))) {\n          specifiers.push(\n            t.exportSpecifier(t.identifier(name), t.identifier(name)),\n          );\n        }\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        path.replaceWith(declaration.node);\n        path.insertAfter(t.exportNamedDeclaration(null, specifiers));\n      },\n\n      // try {} catch ({a, ...b}) {}\n      CatchClause(path) {\n        const paramPath = path.get(\"param\");\n        replaceRestElement(path, paramPath);\n      },\n\n      // ({a, ...b} = c);\n      AssignmentExpression(path, file) {\n        const leftPath = path.get(\"left\");\n        if (leftPath.isObjectPattern() && hasRestElement(leftPath)) {\n          const nodes = [];\n\n          const refName = path.scope.generateUidBasedOnNode(\n            path.node.right,\n            \"ref\",\n          );\n\n          nodes.push(\n            t.variableDeclaration(\"var\", [\n              t.variableDeclarator(t.identifier(refName), path.node.right),\n            ]),\n          );\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(leftPath, file, t.identifier(refName));\n\n          if (impureComputedPropertyDeclarators.length > 0) {\n            nodes.push(\n              t.variableDeclaration(\"var\", impureComputedPropertyDeclarators),\n            );\n          }\n\n          const nodeWithoutSpread = t.cloneNode(path.node);\n          nodeWithoutSpread.right = t.identifier(refName);\n          nodes.push(t.expressionStatement(nodeWithoutSpread));\n          nodes.push(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", argument, callExpression),\n            ),\n          );\n          nodes.push(t.expressionStatement(t.identifier(refName)));\n\n          path.replaceWithMultiple(nodes);\n        }\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const leftPath = path.get(\"left\");\n        const left = node.left;\n\n        if (!hasObjectPatternRestElement(leftPath)) {\n          return;\n        }\n\n        if (!t.isVariableDeclaration(left)) {\n          // for ({a, ...b} of []) {}\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n          const body = path.node.body as t.BlockStatement;\n\n          if (body.body.length === 0 && path.isCompletionRecord()) {\n            body.body.unshift(\n              t.expressionStatement(scope.buildUndefinedNode()),\n            );\n          }\n\n          body.body.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", left, t.cloneNode(temp)),\n            ),\n          );\n        } else {\n          // for (var {a, ...b} of []) {}\n          const pattern = left.declarations[0].id;\n\n          const key = scope.generateUidIdentifier(\"ref\");\n          node.left = t.variableDeclaration(left.kind, [\n            t.variableDeclarator(key, null),\n          ]);\n\n          path.ensureBlock();\n          const body = node.body as t.BlockStatement;\n\n          body.body.unshift(\n            t.variableDeclaration(node.left.kind, [\n              t.variableDeclarator(pattern, t.cloneNode(key)),\n            ]),\n          );\n        }\n      },\n\n      // [{a, ...b}] = c;\n      ArrayPattern(path) {\n        const objectPatterns: t.VariableDeclarator[] = [];\n\n        visitRestElements(path, path => {\n          if (!path.parentPath.isObjectPattern()) {\n            // Return early if the parent is not an ObjectPattern, but\n            // (for example) an ArrayPattern or Function, because that\n            // means this RestElement is an not an object property.\n            return;\n          }\n\n          const objectPattern = path.parentPath;\n\n          const uid = path.scope.generateUidIdentifier(\"ref\");\n          objectPatterns.push(t.variableDeclarator(objectPattern.node, uid));\n\n          objectPattern.replaceWith(t.cloneNode(uid));\n          path.skip();\n        });\n\n        if (objectPatterns.length > 0) {\n          const statementPath = path.getStatementParent();\n          const statementNode = statementPath.node;\n          const kind =\n            statementNode.type === \"VariableDeclaration\"\n              ? statementNode.kind\n              : \"var\";\n          statementPath.insertAfter(\n            t.variableDeclaration(kind, objectPatterns),\n          );\n        }\n      },\n\n      // var a = { ...b, ...c }\n      ObjectExpression(path, file) {\n        if (!hasSpread(path.node)) return;\n\n        let helper: t.Identifier | t.MemberExpression;\n        if (setSpreadProperties) {\n          helper = getExtendsHelper(file);\n        } else {\n          if (process.env.BABEL_8_BREAKING) {\n            helper = file.addHelper(\"objectSpread2\");\n          } else {\n            try {\n              helper = file.addHelper(\"objectSpread2\");\n            } catch {\n              // TODO: This is needed to workaround https://github.com/babel/babel/issues/10187\n              // and https://github.com/babel/babel/issues/10179 for older @babel/core versions\n              // where #10187 isn't fixed.\n              this.file.declarations[\"objectSpread2\"] = null;\n\n              // objectSpread2 has been introduced in v7.5.0\n              // We have to maintain backward compatibility.\n              helper = file.addHelper(\"objectSpread\");\n            }\n          }\n        }\n\n        let exp: t.CallExpression = null;\n        let props: t.ObjectMember[] = [];\n\n        function make() {\n          const hadProps = props.length > 0;\n          const obj = t.objectExpression(props);\n          props = [];\n\n          if (!exp) {\n            exp = t.callExpression(helper, [obj]);\n            return;\n          }\n\n          // When we can assume that getters are pure and don't depend on\n          // the order of evaluation, we can avoid making multiple calls.\n          if (pureGetters) {\n            if (hadProps) {\n              exp.arguments.push(obj);\n            }\n            return;\n          }\n\n          exp = t.callExpression(t.cloneNode(helper), [\n            exp,\n            // If we have static props, we need to insert an empty object\n            // because the odd arguments are copied with [[Get]], not\n            // [[GetOwnProperty]]\n            ...(hadProps ? [t.objectExpression([]), obj] : []),\n          ]);\n        }\n\n        for (const prop of path.node.properties) {\n          if (t.isSpreadElement(prop)) {\n            make();\n            exp.arguments.push(prop.argument);\n          } else {\n            props.push(prop);\n          }\n        }\n\n        if (props.length) make();\n\n        path.replaceWith(exp);\n      },\n    },\n  };\n});\n"], "names": ["shouldStoreRHSInTemporaryVariable", "node", "type", "nonNullElements", "elements", "filter", "element", "length", "properties", "firstProperty", "value", "left", "argument", "chrome", "opera", "edge", "firefox", "safari", "deno", "ios", "samsung", "opera_mobile", "electron", "isAssignmentPattern", "isObjectProperty", "t", "identifier", "property", "objectProperty", "pattern", "objectPattern", "ZERO_REFS", "isReferenced", "declare", "api", "opts", "_api$assumption", "_api$assumption2", "_api$assumption3", "_api$assumption4", "assertVersion", "targets", "supportsObjectAssign", "isRequired", "compatData", "useBuiltIns", "loose", "Error", "ignoreFunctionLength", "assumption", "objectRestNoSymbols", "pureGetters", "setSpreadProperties", "getExtendsHelper", "file", "memberExpression", "addHelper", "hasRestElement", "path", "foundRestElement", "visitRestElements", "restElement", "stop", "hasObjectPatternRestElement", "parentPath", "isObjectPattern", "visitor", "traverse", "Expression", "parent", "key", "computed", "skip", "RestElement", "hasSpread", "prop", "isSpreadElement", "extractNormalizedKeys", "props", "keys", "allPrimitives", "hasTemplateLiteral", "isIdentifier", "push", "stringLiteral", "name", "isTemplateLiteral", "cloneNode", "isLiteral", "String", "isMemberExpression", "object", "isCallExpression", "matchesPattern", "callee", "replaceImpureComputedKeys", "scope", "impureComputedPropertyDeclarators", "prop<PERSON>ath", "get", "isPure", "generateUidBasedOnNode", "declarator", "variableDeclarator", "replaceWith", "removeUnusedExcludedKeys", "bindings", "getOuterBindingIdentifierPaths", "Object", "for<PERSON>ach", "bindingName", "bindingParentPath", "getBinding", "references", "remove", "createObjectRest", "objRef", "last", "assertRestElement", "callExpression", "objectExpression", "sequenceExpression", "keyExpression", "arrayExpression", "isProgram", "block", "program", "findParent", "id", "generateUidIdentifier", "init", "kind", "replaceRestElement", "<PERSON><PERSON><PERSON><PERSON>", "container", "isArrayPattern", "i", "uid", "declar", "variableDeclaration", "ensureBlock", "unshiftContainer", "manipulateOptions", "_", "parser", "plugins", "Function", "params", "paramsWithRestElement", "Set", "idsInRestParams", "param", "add", "getBindingIdentifiers", "idInRest", "IdentifierHandler", "functionScope", "has", "isReferencedIdentifier", "isBindingIdentifier", "shouldTransformParam", "idx", "convertFunctionParams", "VariableDeclarator", "insertionPath", "originalPath", "initRef", "generateUidIdentifierBasedOnNode", "insertBefore", "ref", "refProper<PERSON><PERSON>ath", "unshift", "isVariableDeclarator", "impureObjRefComputedDeclarators", "objectPatternPath", "assertIdentifier", "insertAfter", "registerBinding", "ExportNamedDeclaration", "declaration", "isVariableDeclaration", "hasRest", "some", "specifiers", "getOuterBindingIdentifiers", "exportSpecifier", "exportNamedDeclaration", "CatchClause", "AssignmentExpression", "leftPath", "nodes", "refName", "right", "nodeWithoutSpread", "expressionStatement", "assignmentExpression", "replaceWithMultiple", "ForXStatement", "temp", "body", "isCompletionRecord", "buildUndefinedNode", "declarations", "ArrayPattern", "objectPatterns", "statementPath", "getStatementParent", "statementNode", "ObjectExpression", "helper", "_unused", "exp", "make", "hadProps", "obj", "arguments"], "mappings": ";;;;;;;;;AASe,SAASA,iCAAiCA,CACvDC,IAAY,EACH;AACT,EAAA,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK,CAAA;AACvB,EAAA,IAAIA,IAAI,CAACC,IAAI,KAAK,cAAc,EAAE;AAChC,IAAA,MAAMC,eAAe,GAAGF,IAAI,CAACG,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC,CAAA;AACzE,IAAA,IAAIH,eAAe,CAACI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KACvC,OAAOP,iCAAiC,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;AACnE,GAAC,MAAM,IAAIF,IAAI,CAACC,IAAI,KAAK,eAAe,EAAE;IACxC,MAAM;AAAEM,MAAAA,UAAAA;AAAW,KAAC,GAAGP,IAAI,CAAA;IAC3B,IAAIO,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAClC,IAAIC,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,KAC1C;AACH,MAAA,MAAME,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAA;AACnC,MAAA,IAAIC,aAAa,CAACP,IAAI,KAAK,gBAAgB,EAAE;AAE3C,QAAA,OAAOF,iCAAiC,CAACS,aAAa,CAACC,KAAe,CAAC,CAAA;AACzE,OAAC,MAAM;QACL,OAAOV,iCAAiC,CAACS,aAAa,CAAC,CAAA;AACzD,OAAA;AACF,KAAA;AACF,GAAC,MAAM,IAAIR,IAAI,CAACC,IAAI,KAAK,mBAAmB,EAAE;AAC5C,IAAA,OAAOF,iCAAiC,CAACC,IAAI,CAACU,IAAI,CAAC,CAAA;AACrD,GAAC,MAAM,IAAIV,IAAI,CAACC,IAAI,KAAK,aAAa,EAAE;IACtC,IAAID,IAAI,CAACW,QAAQ,CAACV,IAAI,KAAK,YAAY,EAAE,OAAO,IAAI,CAAA;AACpD,IAAA,OAAOF,iCAAiC,CAACC,IAAI,CAACW,QAAQ,CAAC,CAAA;AACzD,GAAC,MAAM;AAEL,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF;;ACvCA,iBAAe;AACb,EAAA,eAAe,EAAE;AACfC,IAAAA,MAAM,EAAE,IAAI;AACZC,IAAAA,KAAK,EAAE,IAAI;AACXC,IAAAA,IAAI,EAAE,IAAI;AACVC,IAAAA,OAAO,EAAE,IAAI;AACbC,IAAAA,MAAM,EAAE,IAAI;AACZhB,IAAAA,IAAI,EAAE,GAAG;AACTiB,IAAAA,IAAI,EAAE,GAAG;AACTC,IAAAA,GAAG,EAAE,IAAI;AACTC,IAAAA,OAAO,EAAE,GAAG;AACZC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,QAAQ,EAAE,MAAA;AACZ,GAAA;AACF,CAAC;;ACND,MAAM;EAAEC,mBAAmB;AAAEC,EAAAA,gBAAAA;AAAiB,CAAC,GAAGC,UAAC,CAAA;AAGhB;AACjC,EAAA,MAAMxB,IAAI,GAAGwB,UAAC,CAACC,UAAU,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,MAAMC,QAAQ,GAAGF,UAAC,CAACG,cAAc,CAACH,UAAC,CAACC,UAAU,CAAC,KAAK,CAAC,EAAEzB,IAAI,CAAC,CAAA;EAC5D,MAAM4B,OAAO,GAAGJ,UAAC,CAACK,aAAa,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAA;AAG3C,EAAA,IAAII,SAAS,GAAGN,UAAC,CAACO,YAAY,CAAC/B,IAAI,EAAE0B,QAAQ,EAAEE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACjE,CAAA;AAQA,YAAeI,yBAAO,CAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;AAAA,EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,CAAA;EAC7CL,GAAG,CAACM,aAAa,CAAA,sCAAoB,CAAC,CAAA;AAEtC,EAAA,MAAMC,OAAO,GAAGP,GAAG,CAACO,OAAO,EAAE,CAAA;EAC7B,MAAMC,oBAAoB,GAAG,CAACC,mCAAU,CAAC,eAAe,EAAEF,OAAO,EAAE;AACjEG,IAAAA,UAAAA;AACF,GAAC,CAAC,CAAA;EAEF,MAAM;AAAEC,IAAAA,WAAW,GAAGH,oBAAoB;AAAEI,IAAAA,KAAK,GAAG,KAAA;AAAM,GAAC,GAAGX,IAAI,CAAA;AAElE,EAAA,IAAI,OAAOW,KAAK,KAAK,SAAS,EAAE;AAC9B,IAAA,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC,CAAA;AAC3D,GAAA;AAEA,EAAA,MAAMC,oBAAoB,GAAA,CAAAZ,eAAA,GAAGF,GAAG,CAACe,UAAU,CAAC,sBAAsB,CAAC,KAAAb,IAAAA,GAAAA,eAAA,GAAIU,KAAK,CAAA;AAC5E,EAAA,MAAMI,mBAAmB,GAAA,CAAAb,gBAAA,GAAGH,GAAG,CAACe,UAAU,CAAC,qBAAqB,CAAC,KAAAZ,IAAAA,GAAAA,gBAAA,GAAIS,KAAK,CAAA;AAC1E,EAAA,MAAMK,WAAW,GAAA,CAAAb,gBAAA,GAAGJ,GAAG,CAACe,UAAU,CAAC,aAAa,CAAC,KAAAX,IAAAA,GAAAA,gBAAA,GAAIQ,KAAK,CAAA;AAC1D,EAAA,MAAMM,mBAAmB,GAAA,CAAAb,gBAAA,GAAGL,GAAG,CAACe,UAAU,CAAC,qBAAqB,CAAC,KAAAV,IAAAA,GAAAA,gBAAA,GAAIO,KAAK,CAAA;EAE1E,SAASO,gBAAgBA,CACvBC,IAAgB,EACmB;IACnC,OAAOT,WAAW,GACdpB,UAAC,CAAC8B,gBAAgB,CAAC9B,UAAC,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAED,UAAC,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClE4B,IAAI,CAACE,SAAS,CAAC,SAAS,CAAC,CAAA;AAC/B,GAAA;EAEA,SAASC,cAAcA,CAACC,IAAW,EAAE;IACnC,IAAIC,gBAAgB,GAAG,KAAK,CAAA;AAC5BC,IAAAA,iBAAiB,CAACF,IAAI,EAAEG,WAAW,IAAI;AACrCF,MAAAA,gBAAgB,GAAG,IAAI,CAAA;MACvBE,WAAW,CAACC,IAAI,EAAE,CAAA;AACpB,KAAC,CAAC,CAAA;AACF,IAAA,OAAOH,gBAAgB,CAAA;AACzB,GAAA;EAEA,SAASI,2BAA2BA,CAACL,IAAc,EAAW;IAC5D,IAAIC,gBAAgB,GAAG,KAAK,CAAA;AAC5BC,IAAAA,iBAAiB,CAACF,IAAI,EAAEG,WAAW,IAAI;AACrC,MAAA,IAAIA,WAAW,CAACG,UAAU,CAACC,eAAe,EAAE,EAAE;AAC5CN,QAAAA,gBAAgB,GAAG,IAAI,CAAA;QACvBE,WAAW,CAACC,IAAI,EAAE,CAAA;AACpB,OAAA;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAOH,gBAAgB,CAAA;AACzB,GAAA;AAEA,EAAA,SAASC,iBAAiBA,CACxBF,IAAc,EACdQ,OAA+C,EAC/C;IACAR,IAAI,CAACS,QAAQ,CAAC;MACZC,UAAUA,CAACV,IAAI,EAAE;QACf,MAAM;UAAEW,MAAM;AAAEC,UAAAA,GAAAA;AAAI,SAAC,GAAGZ,IAAI,CAAA;QAC5B,IACGnC,mBAAmB,CAAC8C,MAAM,CAAC,IAAIC,GAAG,KAAK,OAAO,IAC9C9C,gBAAgB,CAAC6C,MAAM,CAAC,IAAIA,MAAM,CAACE,QAAQ,IAAID,GAAG,KAAK,KAAM,EAC9D;UACAZ,IAAI,CAACc,IAAI,EAAE,CAAA;AACb,SAAA;OACD;AACDC,MAAAA,WAAW,EAAEP,OAAAA;AACf,KAAC,CAAC,CAAA;AACJ,GAAA;EAEA,SAASQ,SAASA,CAACzE,IAAwB,EAAW;AACpD,IAAA,KAAK,MAAM0E,IAAI,IAAI1E,IAAI,CAACO,UAAU,EAAE;AAClC,MAAA,IAAIiB,UAAC,CAACmD,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3B,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;EAQA,SAASE,qBAAqBA,CAAC5E,IAAqB,EAAE;AAEpD,IAAA,MAAM6E,KAAK,GAAG7E,IAAI,CAACO,UAAgC,CAAA;IACnD,MAAMuE,IAAoB,GAAG,EAAE,CAAA;IAC/B,IAAIC,aAAa,GAAG,IAAI,CAAA;IACxB,IAAIC,kBAAkB,GAAG,KAAK,CAAA;AAE9B,IAAA,KAAK,MAAMN,IAAI,IAAIG,KAAK,EAAE;MACxB,MAAM;AAAER,QAAAA,GAAAA;AAAI,OAAC,GAAGK,IAAI,CAAA;MACpB,IAAIlD,UAAC,CAACyD,YAAY,CAACZ,GAAG,CAAC,IAAI,CAACK,IAAI,CAACJ,QAAQ,EAAE;QAEzCQ,IAAI,CAACI,IAAI,CAAC1D,UAAC,CAAC2D,aAAa,CAACd,GAAG,CAACe,IAAI,CAAC,CAAC,CAAA;OACrC,MAAM,IAAI5D,UAAC,CAAC6D,iBAAiB,CAAChB,GAAG,CAAC,EAAE;QACnCS,IAAI,CAACI,IAAI,CAAC1D,UAAC,CAAC8D,SAAS,CAACjB,GAAG,CAAC,CAAC,CAAA;AAC3BW,QAAAA,kBAAkB,GAAG,IAAI,CAAA;OAC1B,MAAM,IAAIxD,UAAC,CAAC+D,SAAS,CAAClB,GAAG,CAAC,EAAE;AAC3BS,QAAAA,IAAI,CAACI,IAAI,CACP1D,UAAC,CAAC2D,aAAa,CACbK,MAAM,CAEJnB,GAAG,CAAC5D,KACN,CACF,CACF,CAAC,CAAA;AACH,OAAC,MAAM;QAELqE,IAAI,CAACI,IAAI,CAAC1D,UAAC,CAAC8D,SAAS,CAACjB,GAAG,CAAC,CAAC,CAAA;AAE3B,QAAA,IACG7C,UAAC,CAACiE,kBAAkB,CAACpB,GAAG,EAAE;AAAEC,UAAAA,QAAQ,EAAE,KAAA;SAAO,CAAC,IAC7C9C,UAAC,CAACyD,YAAY,CAACZ,GAAG,CAACqB,MAAM,EAAE;AAAEN,UAAAA,IAAI,EAAE,QAAA;SAAU,CAAC,IAC/C5D,UAAC,CAACmE,gBAAgB,CAACtB,GAAG,CAAC,IACtB7C,UAAC,CAACoE,cAAc,CAACvB,GAAG,CAACwB,MAAM,EAAE,YAAY,CAAE,EAC7C,CAED,MAAM;AACLd,UAAAA,aAAa,GAAG,KAAK,CAAA;AACvB,SAAA;AACF,OAAA;AACF,KAAA;IAEA,OAAO;MAAED,IAAI;MAAEC,aAAa;AAAEC,MAAAA,kBAAAA;KAAoB,CAAA;AACpD,GAAA;AAIA,EAAA,SAASc,yBAAyBA,CAChCvF,UAAwC,EACxCwF,KAAY,EACZ;IACA,MAAMC,iCAAyD,GAAG,EAAE,CAAA;AACpE,IAAA,KAAK,MAAMC,QAAQ,IAAI1F,UAAU,EAAE;AAEjC,MAAA,MAAM8D,GAAG,GAAG4B,QAAQ,CAACC,GAAG,CAAC,KAAK,CAA2B,CAAA;AACzD,MAAA,IAAID,QAAQ,CAACjG,IAAI,CAACsE,QAAQ,IAAI,CAACD,GAAG,CAAC8B,MAAM,EAAE,EAAE;QAC3C,MAAMf,IAAI,GAAGW,KAAK,CAACK,sBAAsB,CAAC/B,GAAG,CAACrE,IAAI,CAAC,CAAA;AACnD,QAAA,MAAMqG,UAAU,GAAG7E,UAAC,CAAC8E,kBAAkB,CAAC9E,UAAC,CAACC,UAAU,CAAC2D,IAAI,CAAC,EAAEf,GAAG,CAACrE,IAAI,CAAC,CAAA;AACrEgG,QAAAA,iCAAiC,CAACd,IAAI,CAACmB,UAAU,CAAC,CAAA;QAClDhC,GAAG,CAACkC,WAAW,CAAC/E,UAAC,CAACC,UAAU,CAAC2D,IAAI,CAAC,CAAC,CAAA;AACrC,OAAA;AACF,KAAA;AACA,IAAA,OAAOY,iCAAiC,CAAA;AAC1C,GAAA;EAEA,SAASQ,wBAAwBA,CAAC/C,IAA+B,EAAQ;AACvE,IAAA,MAAMgD,QAAQ,GAAGhD,IAAI,CAACiD,8BAA8B,EAAE,CAAA;IAEtDC,MAAM,CAAC7B,IAAI,CAAC2B,QAAQ,CAAC,CAACG,OAAO,CAACC,WAAW,IAAI;AAC3C,MAAA,MAAMC,iBAAiB,GAAGL,QAAQ,CAACI,WAAW,CAAC,CAAC9C,UAAU,CAAA;AAC1D,MAAA,IACEN,IAAI,CAACsC,KAAK,CAACgB,UAAU,CAACF,WAAW,CAAC,CAACG,UAAU,GACPlF,SAAU,IAChD,CAACgF,iBAAiB,CAACvF,gBAAgB,EAAE,EACrC;AACA,QAAA,OAAA;AACF,OAAA;MACAuF,iBAAiB,CAACG,MAAM,EAAE,CAAA;AAC5B,KAAC,CAAC,CAAA;AACJ,GAAA;AAGA,EAAA,SAASC,gBAAgBA,CACvBzD,IAA+B,EAC/BJ,IAAgB,EAChB8D,MAAyC,EAKzC;AACA,IAAA,MAAMtC,KAAK,GAAGpB,IAAI,CAACyC,GAAG,CAAC,YAAY,CAAC,CAAA;IACpC,MAAMkB,IAAI,GAAGvC,KAAK,CAACA,KAAK,CAACvE,MAAM,GAAG,CAAC,CAAC,CAAA;AACpCkB,IAAAA,UAAC,CAAC6F,iBAAiB,CAACD,IAAI,CAACpH,IAAI,CAAC,CAAA;IAC9B,MAAM4D,WAAW,GAAGpC,UAAC,CAAC8D,SAAS,CAAC8B,IAAI,CAACpH,IAAI,CAAC,CAAA;IAC1CoH,IAAI,CAACH,MAAM,EAAE,CAAA;AAEb,IAAA,MAAMjB,iCAAiC,GAAGF,yBAAyB,CACjErC,IAAI,CAACyC,GAAG,CAAC,YAAY,CAAC,EACtBzC,IAAI,CAACsC,KACP,CAAC,CAAA;IACD,MAAM;MAAEjB,IAAI;MAAEC,aAAa;AAAEC,MAAAA,kBAAAA;AAAmB,KAAC,GAAGJ,qBAAqB,CACvEnB,IAAI,CAACzD,IACP,CAAC,CAAA;AAED,IAAA,IAAI8E,IAAI,CAACxE,MAAM,KAAK,CAAC,EAAE;AACrB,MAAA,OAAO,CACL0F,iCAAiC,EACjCpC,WAAW,CAACjD,QAAQ,EACpBa,UAAC,CAAC8F,cAAc,CAAClE,gBAAgB,CAACC,IAAI,CAAC,EAAE,CACvC7B,UAAC,CAAC+F,gBAAgB,CAAC,EAAE,CAAC,EACtB/F,UAAC,CAACgG,kBAAkB,CAAC,CACnBhG,UAAC,CAAC8F,cAAc,CAACjE,IAAI,CAACE,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAC3D/B,UAAC,CAAC8D,SAAS,CAAC6B,MAAM,CAAC,CACpB,CAAC,EACF3F,UAAC,CAAC8D,SAAS,CAAC6B,MAAM,CAAC,CACpB,CAAC,CACH,CAAC,CACH,CAAA;AACH,KAAA;AAEA,IAAA,IAAIM,aAAa,CAAA;IACjB,IAAI,CAAC1C,aAAa,EAAE;AAElB0C,MAAAA,aAAa,GAAGjG,UAAC,CAAC8F,cAAc,CAC9B9F,UAAC,CAAC8B,gBAAgB,CAAC9B,UAAC,CAACkG,eAAe,CAAC5C,IAAI,CAAC,EAAEtD,UAAC,CAACC,UAAU,CAAC,KAAK,CAAC,CAAC,EAChE,CAAC4B,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAClC,CAAC,CAAA;AACH,KAAC,MAAM;AACLkE,MAAAA,aAAa,GAAGjG,UAAC,CAACkG,eAAe,CAAC5C,IAAI,CAAC,CAAA;AAEvC,MAAA,IAAI,CAACE,kBAAkB,IAAI,CAACxD,UAAC,CAACmG,SAAS,CAAClE,IAAI,CAACsC,KAAK,CAAC6B,KAAK,CAAC,EAAE;AAEzD,QAAA,MAAMC,OAAO,GAAGpE,IAAI,CAACqE,UAAU,CAACrE,IAAI,IAAIA,IAAI,CAACkE,SAAS,EAAE,CAAC,CAAA;QACzD,MAAMI,EAAE,GAAGtE,IAAI,CAACsC,KAAK,CAACiC,qBAAqB,CAAC,UAAU,CAAC,CAAA;AAEvDH,QAAAA,OAAO,CAAC9B,KAAK,CAACb,IAAI,CAAC;UACjB6C,EAAE;AACFE,UAAAA,IAAI,EAAER,aAAa;AACnBS,UAAAA,IAAI,EAAE,OAAA;AACR,SAAC,CAAC,CAAA;AAEFT,QAAAA,aAAa,GAAGjG,UAAC,CAAC8D,SAAS,CAACyC,EAAE,CAAC,CAAA;AACjC,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,CACL/B,iCAAiC,EACjCpC,WAAW,CAACjD,QAAQ,EACpBa,UAAC,CAAC8F,cAAc,CACdjE,IAAI,CAACE,SAAS,CACZ,CAA0BN,uBAAAA,EAAAA,mBAAmB,GAAG,OAAO,GAAG,EAAE,CAAA,CAC9D,CAAC,EACD,CAACzB,UAAC,CAAC8D,SAAS,CAAC6B,MAAM,CAAC,EAAEM,aAAa,CACrC,CAAC,CACF,CAAA;AACH,GAAA;AAEA,EAAA,SAASU,kBAAkBA,CACzBpE,UAAgD,EAChDqE,SAEC,EACDC,SAAmC,EAC7B;AACN,IAAA,IAAID,SAAS,CAAC9G,mBAAmB,EAAE,EAAE;MACnC6G,kBAAkB,CAACpE,UAAU,EAAEqE,SAAS,CAAClC,GAAG,CAAC,MAAM,CAAC,EAAEmC,SAAS,CAAC,CAAA;AAChE,MAAA,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAACE,cAAc,EAAE,IAAI9E,cAAc,CAAC4E,SAAS,CAAC,EAAE;AAC3D,MAAA,MAAMjI,QAAQ,GAAGiI,SAAS,CAAClC,GAAG,CAAC,UAAU,CAAC,CAAA;AAE1C,MAAA,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpI,QAAQ,CAACG,MAAM,EAAEiI,CAAC,EAAE,EAAE;QACxCJ,kBAAkB,CAACpE,UAAU,EAAE5D,QAAQ,CAACoI,CAAC,CAAC,EAAEF,SAAS,CAAC,CAAA;AACxD,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAACpE,eAAe,EAAE,IAAIR,cAAc,CAAC4E,SAAS,CAAC,EAAE;MAC5D,MAAMI,GAAG,GAAGzE,UAAU,CAACgC,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;MAEzD,MAAMS,MAAM,GAAGjH,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAE,CAC1ClH,UAAC,CAAC8E,kBAAkB,CAAC8B,SAAS,CAACpI,IAAI,EAAEwI,GAAG,CAAC,CAC1C,CAAC,CAAA;AAEF,MAAA,IAAIH,SAAS,EAAE;AACbA,QAAAA,SAAS,CAACnD,IAAI,CAACuD,MAAM,CAAC,CAAA;AACxB,OAAC,MAAM;QACL1E,UAAU,CAAC4E,WAAW,EAAE,CAAA;QACvB5E,UAAU,CAACmC,GAAG,CAAC,MAAM,CAAC,CAAgC0C,gBAAgB,CACrE,MAAM,EACNH,MACF,CAAC,CAAA;AACH,OAAA;MACAL,SAAS,CAAC7B,WAAW,CAAC/E,UAAC,CAAC8D,SAAS,CAACkD,GAAG,CAAC,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;EAEA,OAAO;AACLpD,IAAAA,IAAI,EAAE,8BAA8B;AACpCyD,IAAAA,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAAC9D,IAAI,CAAC,kBAAkB,CAAC;AAE1DjB,IAAAA,OAAO,EAAE;MAEPgF,QAAQA,CAACxF,IAAI,EAAE;AACb,QAAA,MAAMyF,MAAM,GAAGzF,IAAI,CAACyC,GAAG,CAAC,QAAQ,CAAC,CAAA;AACjC,QAAA,MAAMiD,qBAAqB,GAAG,IAAIC,GAAG,EAAU,CAAA;AAC/C,QAAA,MAAMC,eAAe,GAAG,IAAID,GAAG,EAAE,CAAA;AACjC,QAAA,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,MAAM,CAAC5I,MAAM,EAAE,EAAEiI,CAAC,EAAE;AACtC,UAAA,MAAMe,KAAK,GAAGJ,MAAM,CAACX,CAAC,CAAC,CAAA;AACvB,UAAA,IAAI/E,cAAc,CAAC8F,KAAK,CAAC,EAAE;AACzBH,YAAAA,qBAAqB,CAACI,GAAG,CAAChB,CAAC,CAAC,CAAA;AAC5B,YAAA,KAAK,MAAMnD,IAAI,IAAIuB,MAAM,CAAC7B,IAAI,CAACwE,KAAK,CAACE,qBAAqB,EAAE,CAAC,EAAE;AAC7DH,cAAAA,eAAe,CAACE,GAAG,CAACnE,IAAI,CAAC,CAAA;AAC3B,aAAA;AACF,WAAA;AACF,SAAA;QAKA,IAAIqE,QAAQ,GAAG,KAAK,CAAA;AAEpB,QAAA,MAAMC,iBAAiB,GAAG,UACxBjG,IAA4B,EAC5BkG,aAAoB,EACpB;AACA,UAAA,MAAMvE,IAAI,GAAG3B,IAAI,CAACzD,IAAI,CAACoF,IAAI,CAAA;UAC3B,IACE3B,IAAI,CAACsC,KAAK,CAACgB,UAAU,CAAC3B,IAAI,CAAC,KAAKuE,aAAa,CAAC5C,UAAU,CAAC3B,IAAI,CAAC,IAC9DiE,eAAe,CAACO,GAAG,CAACxE,IAAI,CAAC,EACzB;AACAqE,YAAAA,QAAQ,GAAG,IAAI,CAAA;YACfhG,IAAI,CAACI,IAAI,EAAE,CAAA;AACb,WAAA;SACD,CAAA;AAED,QAAA,IAAI0E,CAAS,CAAA;AACb,QAAA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,MAAM,CAAC5I,MAAM,IAAI,CAACmJ,QAAQ,EAAE,EAAElB,CAAC,EAAE;AAC/C,UAAA,MAAMe,KAAK,GAAGJ,MAAM,CAACX,CAAC,CAAC,CAAA;AACvB,UAAA,IAAI,CAACY,qBAAqB,CAACS,GAAG,CAACrB,CAAC,CAAC,EAAE;YACjC,IAAIe,KAAK,CAACO,sBAAsB,EAAE,IAAIP,KAAK,CAACQ,mBAAmB,EAAE,EAAE;AACjEJ,cAAAA,iBAAiB,CAACJ,KAAK,EAAE7F,IAAI,CAACsC,KAAK,CAAC,CAAA;AACtC,aAAC,MAAM;cACLuD,KAAK,CAACpF,QAAQ,CACZ;AACE,gBAAA,uCAAuC,EAAET,IAAI,IAAIA,IAAI,CAACc,IAAI,EAAE;AAC5D,gBAAA,wCAAwC,EAAEmF,iBAAAA;AAC5C,eAAC,EACDjG,IAAI,CAACsC,KACP,CAAC,CAAA;AACH,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAI,CAAC0D,QAAQ,EAAE;AACb,UAAA,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,MAAM,CAAC5I,MAAM,EAAE,EAAEiI,CAAC,EAAE;AACtC,YAAA,MAAMe,KAAK,GAAGJ,MAAM,CAACX,CAAC,CAAC,CAAA;AACvB,YAAA,IAAIY,qBAAqB,CAACS,GAAG,CAACrB,CAAC,CAAC,EAAE;AAChCJ,cAAAA,kBAAkB,CAAC1E,IAAI,EAAE6F,KAAK,CAAC,CAAA;AACjC,aAAA;AACF,WAAA;AACF,SAAC,MAAM;AACL,UAAA,MAAMS,oBAAoB,GAAIC,GAAW,IACvCA,GAAG,IAAIzB,CAAC,GAAG,CAAC,IAAIY,qBAAqB,CAACS,GAAG,CAACI,GAAG,CAAC,CAAA;UAChDC,+CAAqB,CACnBxG,IAAI,EACJV,oBAAoB,EACpBgH,oBAAoB,EACpB5B,kBACF,CAAC,CAAA;AACH,SAAA;OACD;AAID+B,MAAAA,kBAAkBA,CAACzG,IAAI,EAAEJ,IAAI,EAAE;QAC7B,IAAI,CAACI,IAAI,CAACyC,GAAG,CAAC,IAAI,CAAC,CAAClC,eAAe,EAAE,EAAE;AACrC,UAAA,OAAA;AACF,SAAA;QAEA,IAAImG,aAAa,GAAG1G,IAAI,CAAA;QACxB,MAAM2G,YAAY,GAAG3G,IAAI,CAAA;QAEzBE,iBAAiB,CAACF,IAAI,CAACyC,GAAG,CAAC,IAAI,CAAC,EAAEzC,IAAI,IAAI;UACxC,IAAI,CAACA,IAAI,CAACM,UAAU,CAACC,eAAe,EAAE,EAAE;AAItC,YAAA,OAAA;AACF,WAAA;UAEA,IAIEjE,iCAAiC,CAACqK,YAAY,CAACpK,IAAI,CAAC+H,EAAE,CAAC,IACvD,CAACvG,UAAC,CAACyD,YAAY,CAACmF,YAAY,CAACpK,IAAI,CAACiI,IAAI,CAAC,EACvC;AAKA,YAAA,MAAMoC,OAAO,GAAG5G,IAAI,CAACsC,KAAK,CAACuE,gCAAgC,CACzDF,YAAY,CAACpK,IAAI,CAACiI,IAAI,EACtB,KACF,CAAC,CAAA;AAEDmC,YAAAA,YAAY,CAACG,YAAY,CACvB/I,UAAC,CAAC8E,kBAAkB,CAAC+D,OAAO,EAAED,YAAY,CAACpK,IAAI,CAACiI,IAAI,CACtD,CAAC,CAAA;YAEDmC,YAAY,CAAC7D,WAAW,CACtB/E,UAAC,CAAC8E,kBAAkB,CAAC8D,YAAY,CAACpK,IAAI,CAAC+H,EAAE,EAAEvG,UAAC,CAAC8D,SAAS,CAAC+E,OAAO,CAAC,CACjE,CAAC,CAAA;AAED,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,IAAIG,GAAG,GAAGJ,YAAY,CAACpK,IAAI,CAACiI,IAAI,CAAA;UAChC,MAAMwC,eAA6C,GAAG,EAAE,CAAA;AACxD,UAAA,IAAIvC,IAAI,CAAA;AAERzE,UAAAA,IAAI,CAACqE,UAAU,CAAErE,IAAc,IAAc;AAC3C,YAAA,IAAIA,IAAI,CAAClC,gBAAgB,EAAE,EAAE;AAC3BkJ,cAAAA,eAAe,CAACC,OAAO,CAACjH,IAAI,CAAC,CAAA;AAC/B,aAAC,MAAM,IAAIA,IAAI,CAACkH,oBAAoB,EAAE,EAAE;AACtCzC,cAAAA,IAAI,GAAGzE,IAAI,CAACM,UAAU,CAAC/D,IAAI,CAACkI,IAAI,CAAA;AAChC,cAAA,OAAO,IAAI,CAAA;AACb,aAAA;AACF,WAAC,CAAC,CAAA;UAEF,MAAM0C,+BAA+B,GAAG9E,yBAAyB,CAC/D2E,eAAe,EACfhH,IAAI,CAACsC,KACP,CAAC,CAAA;AACD0E,UAAAA,eAAe,CAAC7D,OAAO,CAAClC,IAAI,IAAI;YAC9B,MAAM;AAAE1E,cAAAA,IAAAA;AAAK,aAAC,GAAG0E,IAAI,CAAA;AACrB8F,YAAAA,GAAG,GAAGhJ,UAAC,CAAC8B,gBAAgB,CACtBkH,GAAG,EACHhJ,UAAC,CAAC8D,SAAS,CAACtF,IAAI,CAACqE,GAAG,CAAC,EACrBrE,IAAI,CAACsE,QAAQ,IAAI9C,UAAC,CAAC+D,SAAS,CAACvF,IAAI,CAACqE,GAAG,CACvC,CAAC,CAAA;AACH,WAAC,CAAC,CAAA;AAGF,UAAA,MAAMwG,iBAA4C,GAAGpH,IAAI,CAACqE,UAAU,CAClErE,IAAI,IAAIA,IAAI,CAACO,eAAe,EAC9B,CAAC,CAAA;AAED,UAAA,MAAM,CAACgC,iCAAiC,EAAErF,QAAQ,EAAE2G,cAAc,CAAC,GACjEJ,gBAAgB,CACd2D,iBAAiB,EACjBxH,IAAI,EACJmH,GACF,CAAC,CAAA;AAEH,UAAA,IAAItH,WAAW,EAAE;YACfsD,wBAAwB,CAACqE,iBAAiB,CAAC,CAAA;AAC7C,WAAA;AAEArJ,UAAAA,UAAC,CAACsJ,gBAAgB,CAACnK,QAAQ,CAAC,CAAA;AAE5BwJ,UAAAA,aAAa,CAACI,YAAY,CAACvE,iCAAiC,CAAC,CAAA;AAE7DmE,UAAAA,aAAa,CAACI,YAAY,CAACK,+BAA+B,CAAC,CAAA;AAE3DT,UAAAA,aAAa,GAAGA,aAAa,CAACY,WAAW,CACvCvJ,UAAC,CAAC8E,kBAAkB,CAAC3F,QAAQ,EAAE2G,cAAc,CAC/C,CAAC,CAAC,CAAC,CAAmC,CAAA;UAEtC7D,IAAI,CAACsC,KAAK,CAACiF,eAAe,CAAC9C,IAAI,EAAEiC,aAAa,CAAC,CAAA;UAE/C,IAAIU,iBAAiB,CAAC7K,IAAI,CAACO,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE;YAClDuK,iBAAiB,CACd/C,UAAU,CACTrE,IAAI,IAAIA,IAAI,CAAClC,gBAAgB,EAAE,IAAIkC,IAAI,CAACkH,oBAAoB,EAC9D,CAAC,CACA1D,MAAM,EAAE,CAAA;AACb,WAAA;AACF,SAAC,CAAC,CAAA;OACH;MAIDgE,sBAAsBA,CAACxH,IAAI,EAAE;AAC3B,QAAA,MAAMyH,WAAW,GAAGzH,IAAI,CAACyC,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,QAAA,IAAI,CAACgF,WAAW,CAACC,qBAAqB,EAAE,EAAE,OAAA;QAE1C,MAAMC,OAAO,GAAGF,WAAW,CACxBhF,GAAG,CAAC,cAAc,CAAC,CACnBmF,IAAI,CAAC5H,IAAI,IAAIK,2BAA2B,CAACL,IAAI,CAACyC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,IAAI,CAACkF,OAAO,EAAE,OAAA;QAEd,MAAME,UAAU,GAAG,EAAE,CAAA;AAErB,QAAA,KAAK,MAAMlG,IAAI,IAAIuB,MAAM,CAAC7B,IAAI,CAACrB,IAAI,CAAC8H,0BAA0B,CAAC,IAAI,CAAC,CAAC,EAAE;UACrED,UAAU,CAACpG,IAAI,CACb1D,UAAC,CAACgK,eAAe,CAAChK,UAAC,CAACC,UAAU,CAAC2D,IAAI,CAAC,EAAE5D,UAAC,CAACC,UAAU,CAAC2D,IAAI,CAAC,CAC1D,CAAC,CAAA;AACH,SAAA;AAKA3B,QAAAA,IAAI,CAAC8C,WAAW,CAAC2E,WAAW,CAAClL,IAAI,CAAC,CAAA;QAClCyD,IAAI,CAACsH,WAAW,CAACvJ,UAAC,CAACiK,sBAAsB,CAAC,IAAI,EAAEH,UAAU,CAAC,CAAC,CAAA;OAC7D;MAGDI,WAAWA,CAACjI,IAAI,EAAE;AAChB,QAAA,MAAM2E,SAAS,GAAG3E,IAAI,CAACyC,GAAG,CAAC,OAAO,CAAC,CAAA;AACnCiC,QAAAA,kBAAkB,CAAC1E,IAAI,EAAE2E,SAAS,CAAC,CAAA;OACpC;AAGDuD,MAAAA,oBAAoBA,CAAClI,IAAI,EAAEJ,IAAI,EAAE;AAC/B,QAAA,MAAMuI,QAAQ,GAAGnI,IAAI,CAACyC,GAAG,CAAC,MAAM,CAAC,CAAA;QACjC,IAAI0F,QAAQ,CAAC5H,eAAe,EAAE,IAAIR,cAAc,CAACoI,QAAQ,CAAC,EAAE;UAC1D,MAAMC,KAAK,GAAG,EAAE,CAAA;AAEhB,UAAA,MAAMC,OAAO,GAAGrI,IAAI,CAACsC,KAAK,CAACK,sBAAsB,CAC/C3C,IAAI,CAACzD,IAAI,CAAC+L,KAAK,EACf,KACF,CAAC,CAAA;AAEDF,UAAAA,KAAK,CAAC3G,IAAI,CACR1D,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAE,CAC3BlH,UAAC,CAAC8E,kBAAkB,CAAC9E,UAAC,CAACC,UAAU,CAACqK,OAAO,CAAC,EAAErI,IAAI,CAACzD,IAAI,CAAC+L,KAAK,CAAC,CAC7D,CACH,CAAC,CAAA;UAED,MAAM,CAAC/F,iCAAiC,EAAErF,QAAQ,EAAE2G,cAAc,CAAC,GACjEJ,gBAAgB,CAAC0E,QAAQ,EAAEvI,IAAI,EAAE7B,UAAC,CAACC,UAAU,CAACqK,OAAO,CAAC,CAAC,CAAA;AAEzD,UAAA,IAAI9F,iCAAiC,CAAC1F,MAAM,GAAG,CAAC,EAAE;YAChDuL,KAAK,CAAC3G,IAAI,CACR1D,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAE1C,iCAAiC,CAChE,CAAC,CAAA;AACH,WAAA;UAEA,MAAMgG,iBAAiB,GAAGxK,UAAC,CAAC8D,SAAS,CAAC7B,IAAI,CAACzD,IAAI,CAAC,CAAA;UAChDgM,iBAAiB,CAACD,KAAK,GAAGvK,UAAC,CAACC,UAAU,CAACqK,OAAO,CAAC,CAAA;UAC/CD,KAAK,CAAC3G,IAAI,CAAC1D,UAAC,CAACyK,mBAAmB,CAACD,iBAAiB,CAAC,CAAC,CAAA;AACpDH,UAAAA,KAAK,CAAC3G,IAAI,CACR1D,UAAC,CAACyK,mBAAmB,CACnBzK,UAAC,CAAC0K,oBAAoB,CAAC,GAAG,EAAEvL,QAAQ,EAAE2G,cAAc,CACtD,CACF,CAAC,CAAA;AACDuE,UAAAA,KAAK,CAAC3G,IAAI,CAAC1D,UAAC,CAACyK,mBAAmB,CAACzK,UAAC,CAACC,UAAU,CAACqK,OAAO,CAAC,CAAC,CAAC,CAAA;AAExDrI,UAAAA,IAAI,CAAC0I,mBAAmB,CAACN,KAAK,CAAC,CAAA;AACjC,SAAA;OACD;MAGDO,aAAaA,CAAC3I,IAA+B,EAAE;QAC7C,MAAM;UAAEzD,IAAI;AAAE+F,UAAAA,KAAAA;AAAM,SAAC,GAAGtC,IAAI,CAAA;AAC5B,QAAA,MAAMmI,QAAQ,GAAGnI,IAAI,CAACyC,GAAG,CAAC,MAAM,CAAC,CAAA;AACjC,QAAA,MAAMxF,IAAI,GAAGV,IAAI,CAACU,IAAI,CAAA;AAEtB,QAAA,IAAI,CAACoD,2BAA2B,CAAC8H,QAAQ,CAAC,EAAE;AAC1C,UAAA,OAAA;AACF,SAAA;AAEA,QAAA,IAAI,CAACpK,UAAC,CAAC2J,qBAAqB,CAACzK,IAAI,CAAC,EAAE;AAElC,UAAA,MAAM2L,IAAI,GAAGtG,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;AAE/ChI,UAAAA,IAAI,CAACU,IAAI,GAAGc,UAAC,CAACkH,mBAAmB,CAAC,KAAK,EAAE,CACvClH,UAAC,CAAC8E,kBAAkB,CAAC+F,IAAI,CAAC,CAC3B,CAAC,CAAA;UAEF5I,IAAI,CAACkF,WAAW,EAAE,CAAA;AAClB,UAAA,MAAM2D,IAAI,GAAG7I,IAAI,CAACzD,IAAI,CAACsM,IAAwB,CAAA;AAE/C,UAAA,IAAIA,IAAI,CAACA,IAAI,CAAChM,MAAM,KAAK,CAAC,IAAImD,IAAI,CAAC8I,kBAAkB,EAAE,EAAE;AACvDD,YAAAA,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACflJ,UAAC,CAACyK,mBAAmB,CAAClG,KAAK,CAACyG,kBAAkB,EAAE,CAClD,CAAC,CAAA;AACH,WAAA;UAEAF,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACflJ,UAAC,CAACyK,mBAAmB,CACnBzK,UAAC,CAAC0K,oBAAoB,CAAC,GAAG,EAAExL,IAAI,EAAEc,UAAC,CAAC8D,SAAS,CAAC+G,IAAI,CAAC,CACrD,CACF,CAAC,CAAA;AACH,SAAC,MAAM;UAEL,MAAMzK,OAAO,GAAGlB,IAAI,CAAC+L,YAAY,CAAC,CAAC,CAAC,CAAC1E,EAAE,CAAA;AAEvC,UAAA,MAAM1D,GAAG,GAAG0B,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;UAC9ChI,IAAI,CAACU,IAAI,GAAGc,UAAC,CAACkH,mBAAmB,CAAChI,IAAI,CAACwH,IAAI,EAAE,CAC3C1G,UAAC,CAAC8E,kBAAkB,CAACjC,GAAG,EAAE,IAAI,CAAC,CAChC,CAAC,CAAA;UAEFZ,IAAI,CAACkF,WAAW,EAAE,CAAA;AAClB,UAAA,MAAM2D,IAAI,GAAGtM,IAAI,CAACsM,IAAwB,CAAA;AAE1CA,UAAAA,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACflJ,UAAC,CAACkH,mBAAmB,CAAC1I,IAAI,CAACU,IAAI,CAACwH,IAAI,EAAE,CACpC1G,UAAC,CAAC8E,kBAAkB,CAAC1E,OAAO,EAAEJ,UAAC,CAAC8D,SAAS,CAACjB,GAAG,CAAC,CAAC,CAChD,CACH,CAAC,CAAA;AACH,SAAA;OACD;MAGDqI,YAAYA,CAACjJ,IAAI,EAAE;QACjB,MAAMkJ,cAAsC,GAAG,EAAE,CAAA;AAEjDhJ,QAAAA,iBAAiB,CAACF,IAAI,EAAEA,IAAI,IAAI;UAC9B,IAAI,CAACA,IAAI,CAACM,UAAU,CAACC,eAAe,EAAE,EAAE;AAItC,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,MAAMnC,aAAa,GAAG4B,IAAI,CAACM,UAAU,CAAA;UAErC,MAAMyE,GAAG,GAAG/E,IAAI,CAACsC,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;AACnD2E,UAAAA,cAAc,CAACzH,IAAI,CAAC1D,UAAC,CAAC8E,kBAAkB,CAACzE,aAAa,CAAC7B,IAAI,EAAEwI,GAAG,CAAC,CAAC,CAAA;UAElE3G,aAAa,CAAC0E,WAAW,CAAC/E,UAAC,CAAC8D,SAAS,CAACkD,GAAG,CAAC,CAAC,CAAA;UAC3C/E,IAAI,CAACc,IAAI,EAAE,CAAA;AACb,SAAC,CAAC,CAAA;AAEF,QAAA,IAAIoI,cAAc,CAACrM,MAAM,GAAG,CAAC,EAAE;AAC7B,UAAA,MAAMsM,aAAa,GAAGnJ,IAAI,CAACoJ,kBAAkB,EAAE,CAAA;AAC/C,UAAA,MAAMC,aAAa,GAAGF,aAAa,CAAC5M,IAAI,CAAA;AACxC,UAAA,MAAMkI,IAAI,GACR4E,aAAa,CAAC7M,IAAI,KAAK,qBAAqB,GACxC6M,aAAa,CAAC5E,IAAI,GAClB,KAAK,CAAA;UACX0E,aAAa,CAAC7B,WAAW,CACvBvJ,UAAC,CAACkH,mBAAmB,CAACR,IAAI,EAAEyE,cAAc,CAC5C,CAAC,CAAA;AACH,SAAA;OACD;AAGDI,MAAAA,gBAAgBA,CAACtJ,IAAI,EAAEJ,IAAI,EAAE;AAC3B,QAAA,IAAI,CAACoB,SAAS,CAAChB,IAAI,CAACzD,IAAI,CAAC,EAAE,OAAA;AAE3B,QAAA,IAAIgN,MAAyC,CAAA;AAC7C,QAAA,IAAI7J,mBAAmB,EAAE;AACvB6J,UAAAA,MAAM,GAAG5J,gBAAgB,CAACC,IAAI,CAAC,CAAA;AACjC,SAAC,MAAM;AAGE,UAAA;YACL,IAAI;AACF2J,cAAAA,MAAM,GAAG3J,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAAA;aACzC,CAAC,OAAA0J,OAAA,EAAM;cAIN,IAAI,CAAC5J,IAAI,CAACoJ,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;AAI9CO,cAAAA,MAAM,GAAG3J,IAAI,CAACE,SAAS,CAAC,cAAc,CAAC,CAAA;AACzC,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAI2J,GAAqB,GAAG,IAAI,CAAA;QAChC,IAAIrI,KAAuB,GAAG,EAAE,CAAA;QAEhC,SAASsI,IAAIA,GAAG;AACd,UAAA,MAAMC,QAAQ,GAAGvI,KAAK,CAACvE,MAAM,GAAG,CAAC,CAAA;AACjC,UAAA,MAAM+M,GAAG,GAAG7L,UAAC,CAAC+F,gBAAgB,CAAC1C,KAAK,CAAC,CAAA;AACrCA,UAAAA,KAAK,GAAG,EAAE,CAAA;UAEV,IAAI,CAACqI,GAAG,EAAE;YACRA,GAAG,GAAG1L,UAAC,CAAC8F,cAAc,CAAC0F,MAAM,EAAE,CAACK,GAAG,CAAC,CAAC,CAAA;AACrC,YAAA,OAAA;AACF,WAAA;AAIA,UAAA,IAAInK,WAAW,EAAE;AACf,YAAA,IAAIkK,QAAQ,EAAE;AACZF,cAAAA,GAAG,CAACI,SAAS,CAACpI,IAAI,CAACmI,GAAG,CAAC,CAAA;AACzB,aAAA;AACA,YAAA,OAAA;AACF,WAAA;AAEAH,UAAAA,GAAG,GAAG1L,UAAC,CAAC8F,cAAc,CAAC9F,UAAC,CAAC8D,SAAS,CAAC0H,MAAM,CAAC,EAAE,CAC1CE,GAAG,EAIH,IAAIE,QAAQ,GAAG,CAAC5L,UAAC,CAAC+F,gBAAgB,CAAC,EAAE,CAAC,EAAE8F,GAAG,CAAC,GAAG,EAAE,CAAC,CACnD,CAAC,CAAA;AACJ,SAAA;QAEA,KAAK,MAAM3I,IAAI,IAAIjB,IAAI,CAACzD,IAAI,CAACO,UAAU,EAAE;AACvC,UAAA,IAAIiB,UAAC,CAACmD,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3ByI,YAAAA,IAAI,EAAE,CAAA;YACND,GAAG,CAACI,SAAS,CAACpI,IAAI,CAACR,IAAI,CAAC/D,QAAQ,CAAC,CAAA;AACnC,WAAC,MAAM;AACLkE,YAAAA,KAAK,CAACK,IAAI,CAACR,IAAI,CAAC,CAAA;AAClB,WAAA;AACF,SAAA;AAEA,QAAA,IAAIG,KAAK,CAACvE,MAAM,EAAE6M,IAAI,EAAE,CAAA;AAExB1J,QAAAA,IAAI,CAAC8C,WAAW,CAAC2G,GAAG,CAAC,CAAA;AACvB,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}