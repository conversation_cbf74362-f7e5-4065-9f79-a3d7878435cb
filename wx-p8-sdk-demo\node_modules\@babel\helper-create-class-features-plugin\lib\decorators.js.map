{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "_helperSkipTransparentExpressionWrappers", "_fields", "_misc", "incrementId", "id", "idx", "length", "unshift", "current", "createPrivateUidGeneratorForClass", "classPath", "currentPrivateId", "privateNames", "Set", "traverse", "PrivateName", "path", "add", "node", "name", "reifiedId", "String", "fromCharCode", "has", "t", "privateName", "identifier", "createLazyPrivateUidGeneratorForClass", "generator", "replaceClassWithVar", "className", "scope", "type", "varId", "generateUidIdentifierBasedOnNode", "classId", "rename", "get", "replaceWith", "cloneNode", "generateLetUidIdentifier", "parent", "newClassExpr", "classExpression", "superClass", "body", "newPath", "sequenceExpression", "generateClassProperty", "key", "value", "isStatic", "classPrivateProperty", "undefined", "classProperty", "assignIdForAnonymousClass", "generateUidIdentifier", "addProxyAccessorsFor", "element", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isComputed", "version", "thisArg", "thisExpression", "getterBody", "blockStatement", "returnStatement", "memberExpression", "setterBody", "expressionStatement", "assignmentExpression", "getter", "setter", "classPrivateMethod", "classMethod", "insertAfter", "extractProxyAccessorsFor", "template", "expression", "ast", "getComputedKeyLastElement", "skipTransparentExprWrappers", "isSequenceExpression", "expressions", "getComputedKeyMemoiser", "isConstantExpression", "isIdentifier", "hasUid", "isAssignmentExpression", "left", "Error", "toString", "prependExpressionsToComputedKey", "fieldPath", "push", "maybeSequenceExpression", "appendExpressionsToComputedKey", "completion", "scopeParent", "maybeAssignment", "memoiseComputedKey", "generateUid", "expressionSequence", "completionParent", "parentPath", "pushContainer", "prependExpressionsToFieldInitializer", "initializer", "unaryExpression", "prependExpressionsToStaticBlock", "blockPath", "unshiftContainer", "prependExpressionsToConstructor", "constructorPath", "isProtoInitCallExpression", "protoInitCall", "isCallExpression", "callee", "optimizeSuperCallAndExpressions", "protoInitLocal", "mergedSuperCall", "callExpression", "splice", "isThisExpression", "insertExpressionsAfterSuperCallAndOptimize", "CallExpression", "exit", "is<PERSON><PERSON><PERSON>", "newNodes", "map", "expr", "isCompletionRecord", "skip", "ClassMethod", "kind", "createConstructorFromExpressions", "isDerivedClass", "super", "spreadElement", "restElement", "createStaticBlockFromExpressions", "staticBlock", "FIELD", "ACCESSOR", "METHOD", "GETTER", "SETTER", "STATIC_OLD_VERSION", "STATIC", "DECORATORS_HAVE_THIS", "getElementKind", "toSortedDecoratorInfo", "info", "filter", "el", "generateDecorationList", "decorators", "decoratorsThis", "decsCount", "haveOneThis", "some", "Boolean", "decs", "i", "numericLiteral", "haveThis", "generateDecorationExprs", "decorationInfo", "arrayExpression", "flag", "decoratorsHaveThis", "decoratorsArray", "privateMethods", "extractElementLocalAssignments", "localIds", "locals", "Array", "isArray", "addCallAccessorsFor", "getId", "setId", "movePrivateAccessor", "methodLocalVar", "params", "block", "isClassDecoratableElementPath", "staticBlockToIIFE", "arrowFunctionExpression", "staticBlockToFunctionClosure", "functionExpression", "fieldInitializerToClosure", "exprs", "createFunctionExpressionFromPrivateMethod", "isGenerator", "async", "isAsync", "createSetFunctionNameCall", "state", "addHelper", "createToPropertyKeyCall", "propertyKey", "createPrivateBrandCheckClosure", "brandName", "binaryExpression", "usesPrivateField", "traverseFast", "isPrivateName", "_unused", "convertToComputedKey", "computed", "stringLiteral", "hasInstancePrivateAccess", "containsInstancePrivateAccess", "privateNameVisitor", "privateNameVisitorFactory", "privateNamesMap", "stop", "Map", "set", "checkPrivateMethodUpdateError", "decoratedPrivateMethods", "parentParentPath", "buildCodeFrameError", "transformClass", "constant<PERSON>uper", "ignoreFunctionLength", "propertyVisitor", "_path$node$id", "_classDecorationsId", "classDecorators", "hasElementDecorators", "hasComputedKeysSideEffects", "elemDecsUseFnContext", "generateClassPrivateUid", "classAssignments", "memoiseExpression", "hint", "assignments", "localEvaluatedId", "staticInitLocal", "classIdName", "setClassName", "usesFunctionContextOrYieldAwait", "decorator", "isYieldExpression", "isAwaitExpression", "isMetaProperty", "meta", "_unused2", "instancePrivateNames", "elementNode", "static", "isDecorated", "ClassProperty", "ClassPrivateProperty", "ClassAccessorProperty", "_staticInitLocal", "_protoInitLocal", "newId", "newField", "keyP<PERSON>", "elementDecoratorInfo", "classInitLocal", "classIdLocal", "decoratorReceiverId", "handleDecorators", "hasSideEffects", "usesFnContext", "object", "isMemberExpression", "_decoratorReceiverId", "willExtractSomeElemDecs", "needsDeclaraionForClassBinding", "classDecorationsFlag", "classDecorations", "classDecorationsId", "computedKeyAssignments", "isClassDeclaration", "classDecsUsePrivateName", "isClassProperty", "lastInstancePrivateName", "needsInstancePrivateBrandCheck", "fieldInitializerExpressions", "staticFieldInitializerExpressions", "isStaticBlock", "hasDecorators", "isPrivate", "isClassPrivateProperty", "isClassMethod", "nameExpr", "newFieldInitId", "newValue", "initId", "valuePath", "args", "callId", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "file", "refToPreserve", "replace", "remove", "getNextSibling", "initExtraId", "initExtraCall", "elements", "lastComputedElement", "sortedElementDecoratorInfo", "elementDecorations", "elementLocals", "classLocals", "classInitInjected", "classInitCall", "originalClassPath", "originalClass", "staticClosures", "statics", "for<PERSON>ach", "staticBlockClosureId", "fieldValueClosureId", "isClassPrivateMethod", "privateMethodDelegateId", "p", "isRestElement", "staticsClass", "toExpression", "constructorBody", "newExpr", "newExpression", "arguments", "maybeGenerateMemoised", "applyDecoratorWrapper", "applyDecsBody", "firstPublicElement", "createLocalsAssignment", "insertBefore", "classBindingInfo", "getBinding", "constantViolations", "variableDeclaration", "variableDeclarator", "classOuterBindingDelegateLocal", "classOuterBindingLocal", "replaceWithMultiple", "size", "crawl", "maybePrivateBrandName", "lhs", "rhs", "availableHelper", "arrayPattern", "objectPattern", "objectProperty", "isProtoKey", "shouldTransformElement", "shouldTransformClass", "NamedEvaluationVisitoryFactory", "isAnonymous", "visitor", "handleComputedProperty", "propertyPath", "keyValue", "ref", "VariableDeclarator", "AssignmentExpression", "operator", "AssignmentPattern", "ObjectExpression", "isObjectProperty", "isDecoratedAnonymousClassExpression", "isClassExpression", "_default", "assertVersion", "assumption", "loose", "inherits", "_assumption", "_assumption2", "VISITED", "WeakSet", "namedEvaluationVisitor", "visitClass", "_className", "_node$id", "Object", "assign", "ExportDefaultDeclaration", "declaration", "_path$splitExportDecl", "splitExportDeclaration", "NodePath", "prototype", "updatedVarDeclarationPath", "ExportNamedDeclaration", "_path$splitExportDecl2", "Class"], "sources": ["../src/decorators.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, Scope, Visitor } from \"@babel/core\";\nimport { types as t, template } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport type { PluginAPI, PluginObject, PluginPass } from \"@babel/core\";\nimport { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport {\n  privateNameVisitorFactory,\n  type PrivateNameVisitorState,\n} from \"./fields.ts\";\nimport { memoiseComputedKey } from \"./misc.ts\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\ninterface Options {\n  /** @deprecated use `constantSuper` assumption instead. Only supported in 2021-12 version. */\n  loose?: boolean;\n}\n\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty;\n\ntype ClassElement =\n  | ClassDecoratableElement\n  | t.TSDeclareMethod\n  | t.TSIndexSignature\n  | t.StaticBlock;\n\ntype ClassElementCanHaveComputedKeys =\n  | t.ClassMethod\n  | t.ClassProperty\n  | t.ClassAccessorProperty;\n\n// TODO(Babel 8): Only keep 2023-11\nexport type DecoratorVersionKind =\n  | \"2023-11\"\n  | \"2023-05\"\n  | \"2023-01\"\n  | \"2022-03\"\n  | \"2021-12\";\n\nfunction incrementId(id: number[], idx = id.length - 1): void {\n  // If index is -1, id needs an additional character, unshift A\n  if (idx === -1) {\n    id.unshift(charCodes.uppercaseA);\n    return;\n  }\n\n  const current = id[idx];\n\n  if (current === charCodes.uppercaseZ) {\n    // if current is Z, skip to a\n    id[idx] = charCodes.lowercaseA;\n  } else if (current === charCodes.lowercaseZ) {\n    // if current is z, reset to A and carry the 1\n    id[idx] = charCodes.uppercaseA;\n    incrementId(id, idx - 1);\n  } else {\n    // else, increment by one\n    id[idx] = current + 1;\n  }\n}\n\n/**\n * Generates a new private name that is unique to the given class. This can be\n * used to create extra class fields and methods for the implementation, while\n * keeping the length of those names as small as possible. This is important for\n * minification purposes (though private names can generally be minified,\n * transpilations and polyfills cannot yet).\n */\nfunction createPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  const currentPrivateId: number[] = [];\n  const privateNames = new Set<string>();\n\n  classPath.traverse({\n    PrivateName(path) {\n      privateNames.add(path.node.id.name);\n    },\n  });\n\n  return (): t.PrivateName => {\n    let reifiedId;\n    do {\n      incrementId(currentPrivateId);\n      reifiedId = String.fromCharCode(...currentPrivateId);\n    } while (privateNames.has(reifiedId));\n\n    return t.privateName(t.identifier(reifiedId));\n  };\n}\n\n/**\n * Wraps the above generator function so that it's run lazily the first time\n * it's actually required. Several types of decoration do not require this, so it\n * saves iterating the class elements an additional time and allocating the space\n * for the Sets of element names.\n */\nfunction createLazyPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  let generator: () => t.PrivateName;\n\n  return (): t.PrivateName => {\n    if (!generator) {\n      generator = createPrivateUidGeneratorForClass(classPath);\n    }\n\n    return generator();\n  };\n}\n\n/**\n * Takes a class definition and the desired class name if anonymous and\n * replaces it with an equivalent class declaration (path) which is then\n * assigned to a local variable (id). This allows us to reassign the local variable with the\n * decorated version of the class. The class definition retains its original\n * name so that `toString` is not affected, other references to the class\n * are renamed instead.\n */\nfunction replaceClassWithVar(\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n): {\n  id: t.Identifier;\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>;\n} {\n  const id = path.node.id;\n  const scope = path.scope;\n  if (path.type === \"ClassDeclaration\") {\n    const className = id.name;\n    const varId = scope.generateUidIdentifierBasedOnNode(id);\n    const classId = t.identifier(className);\n\n    scope.rename(className, varId.name);\n\n    path.get(\"id\").replaceWith(classId);\n\n    return { id: t.cloneNode(varId), path };\n  } else {\n    let varId: t.Identifier;\n\n    if (id) {\n      className = id.name;\n      varId = generateLetUidIdentifier(scope.parent, className);\n      scope.rename(className, varId.name);\n    } else {\n      varId = generateLetUidIdentifier(\n        scope.parent,\n        typeof className === \"string\" ? className : \"decorated_class\",\n      );\n    }\n\n    const newClassExpr = t.classExpression(\n      typeof className === \"string\" ? t.identifier(className) : null,\n      path.node.superClass,\n      path.node.body,\n    );\n\n    const [newPath] = path.replaceWith(\n      t.sequenceExpression([newClassExpr, varId]),\n    );\n\n    return {\n      id: t.cloneNode(varId),\n      path: newPath.get(\"expressions.0\") as NodePath<t.ClassExpression>,\n    };\n  }\n}\n\nfunction generateClassProperty(\n  key: t.PrivateName | t.Identifier,\n  value: t.Expression | undefined,\n  isStatic: boolean,\n): t.ClassPrivateProperty | t.ClassProperty {\n  if (key.type === \"PrivateName\") {\n    return t.classPrivateProperty(key, value, undefined, isStatic);\n  } else {\n    return t.classProperty(key, value, undefined, undefined, isStatic);\n  }\n}\n\nfunction assignIdForAnonymousClass(\n  path: NodePath<t.Class>,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n) {\n  if (!path.node.id) {\n    path.node.id =\n      typeof className === \"string\"\n        ? t.identifier(className)\n        : path.scope.generateUidIdentifier(\"Class\");\n  }\n}\n\nfunction addProxyAccessorsFor(\n  className: t.Identifier,\n  element: NodePath<ClassDecoratableElement>,\n  getterKey: t.PrivateName | t.Expression,\n  setterKey: t.PrivateName | t.Expression,\n  targetKey: t.PrivateName,\n  isComputed: boolean,\n  isStatic: boolean,\n  version: DecoratorVersionKind,\n): void {\n  const thisArg =\n    (version === \"2023-11\" ||\n      (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n    isStatic\n      ? className\n      : t.thisExpression();\n\n  const getterBody = t.blockStatement([\n    t.returnStatement(\n      t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n    ),\n  ]);\n\n  const setterBody = t.blockStatement([\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n        t.identifier(\"v\"),\n      ),\n    ),\n  ]);\n\n  let getter: t.ClassMethod | t.ClassPrivateMethod,\n    setter: t.ClassMethod | t.ClassPrivateMethod;\n\n  if (getterKey.type === \"PrivateName\") {\n    getter = t.classPrivateMethod(\"get\", getterKey, [], getterBody, isStatic);\n    setter = t.classPrivateMethod(\n      \"set\",\n      setterKey as t.PrivateName,\n      [t.identifier(\"v\")],\n      setterBody,\n      isStatic,\n    );\n  } else {\n    getter = t.classMethod(\n      \"get\",\n      getterKey,\n      [],\n      getterBody,\n      isComputed,\n      isStatic,\n    );\n    setter = t.classMethod(\n      \"set\",\n      setterKey as t.Expression,\n      [t.identifier(\"v\")],\n      setterBody,\n      isComputed,\n      isStatic,\n    );\n  }\n\n  element.insertAfter(setter);\n  element.insertAfter(getter);\n}\n\nfunction extractProxyAccessorsFor(\n  targetKey: t.PrivateName,\n  version: DecoratorVersionKind,\n): (t.FunctionExpression | t.ArrowFunctionExpression)[] {\n  if (version !== \"2023-11\" && version !== \"2023-05\" && version !== \"2023-01\") {\n    return [\n      template.expression.ast`\n        function () {\n          return this.${t.cloneNode(targetKey)};\n        }\n      ` as t.FunctionExpression,\n      template.expression.ast`\n        function (value) {\n          this.${t.cloneNode(targetKey)} = value;\n        }\n      ` as t.FunctionExpression,\n    ];\n  }\n  return [\n    template.expression.ast`\n      o => o.${t.cloneNode(targetKey)}\n    ` as t.ArrowFunctionExpression,\n    template.expression.ast`\n      (o, v) => o.${t.cloneNode(targetKey)} = v\n    ` as t.ArrowFunctionExpression,\n  ];\n}\n\n/**\n * Get the last element for the given computed key path.\n *\n * This function unwraps transparent wrappers and gets the last item when\n * the key is a SequenceExpression.\n *\n * @param {NodePath<t.Expression>} path The key of a computed class element\n * @returns {NodePath<t.Expression>} The simple completion result\n */\nfunction getComputedKeyLastElement(\n  path: NodePath<t.Expression>,\n): NodePath<t.Expression> {\n  path = skipTransparentExprWrappers(path);\n  if (path.isSequenceExpression()) {\n    const expressions = path.get(\"expressions\");\n    return getComputedKeyLastElement(expressions[expressions.length - 1]);\n  }\n  return path;\n}\n\n/**\n * Get a memoiser of the computed key path.\n *\n * This function does not mutate AST. If the computed key is not a constant\n * expression, this function must be called after the key has been memoised.\n *\n * @param {NodePath<t.Expression>} path The key of a computed class element.\n * @returns {t.Expression} A clone of key if key is a constant expression,\n * otherwise a memoiser identifier.\n */\nfunction getComputedKeyMemoiser(path: NodePath<t.Expression>): t.Expression {\n  const element = getComputedKeyLastElement(path);\n  if (element.isConstantExpression()) {\n    return t.cloneNode(path.node);\n  } else if (element.isIdentifier() && path.scope.hasUid(element.node.name)) {\n    return t.cloneNode(path.node);\n  } else if (\n    element.isAssignmentExpression() &&\n    element.get(\"left\").isIdentifier()\n  ) {\n    return t.cloneNode(element.node.left as t.Identifier);\n  } else {\n    throw new Error(\n      `Internal Error: the computed key ${path.toString()} has not yet been memoised.`,\n    );\n  }\n}\n\n/**\n * Prepend expressions to the computed key of the given field path.\n *\n * If the computed key is a sequence expression, this function will unwrap\n * the sequence expression for optimal output size.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction prependExpressionsToComputedKey(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n  >,\n) {\n  const key = fieldPath.get(\"key\") as NodePath<t.Expression>;\n  if (key.isSequenceExpression()) {\n    expressions.push(...key.node.expressions);\n  } else {\n    expressions.push(key.node);\n  }\n  key.replaceWith(maybeSequenceExpression(expressions));\n}\n\n/**\n * Append expressions to the computed key of the given field path.\n *\n * If the computed key is a constant expression or uid reference, it\n * will prepend expressions before the comptued key. Otherwise it will\n * memoise the computed key to preserve its completion result.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction appendExpressionsToComputedKey(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n  >,\n) {\n  const key = fieldPath.get(\"key\") as NodePath<t.Expression>;\n  const completion = getComputedKeyLastElement(key);\n  if (completion.isConstantExpression()) {\n    prependExpressionsToComputedKey(expressions, fieldPath);\n  } else {\n    const scopeParent = key.scope.parent;\n    const maybeAssignment = memoiseComputedKey(\n      completion.node,\n      scopeParent,\n      scopeParent.generateUid(\"computedKey\"),\n    );\n    if (!maybeAssignment) {\n      // If the memoiseComputedKey returns undefined, the key is already a uid reference,\n      // treat it as a constant expression and prepend expressions before it\n      prependExpressionsToComputedKey(expressions, fieldPath);\n    } else {\n      const expressionSequence = [\n        ...expressions,\n        // preserve the completion result\n        t.cloneNode(maybeAssignment.left),\n      ];\n      const completionParent = completion.parentPath;\n      if (completionParent.isSequenceExpression()) {\n        completionParent.pushContainer(\"expressions\", expressionSequence);\n      } else {\n        completion.replaceWith(\n          maybeSequenceExpression([\n            t.cloneNode(maybeAssignment),\n            ...expressionSequence,\n          ]),\n        );\n      }\n    }\n  }\n}\n\n/**\n * Prepend expressions to the field initializer. If the initializer is not defined,\n * this function will wrap the last expression within a `void` unary expression.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction prependExpressionsToFieldInitializer(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n  >,\n) {\n  const initializer = fieldPath.get(\"value\");\n  if (initializer.node) {\n    expressions.push(initializer.node);\n  } else if (expressions.length > 0) {\n    expressions[expressions.length - 1] = t.unaryExpression(\n      \"void\",\n      expressions[expressions.length - 1],\n    );\n  }\n  initializer.replaceWith(maybeSequenceExpression(expressions));\n}\n\nfunction prependExpressionsToStaticBlock(\n  expressions: t.Expression[],\n  blockPath: NodePath<t.StaticBlock>,\n) {\n  blockPath.unshiftContainer(\n    \"body\",\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction prependExpressionsToConstructor(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n) {\n  constructorPath.node.body.body.unshift(\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction isProtoInitCallExpression(\n  expression: t.Expression,\n  protoInitCall: t.Identifier,\n) {\n  return (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee, { name: protoInitCall.name })\n  );\n}\n\n/**\n * Optimize super call and its following expressions\n *\n * @param {t.Expression[]} expressions Mutated by this function. The first element must by a super call\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns optimized expression\n */\nfunction optimizeSuperCallAndExpressions(\n  expressions: t.Expression[],\n  protoInitLocal: t.Identifier,\n) {\n  if (protoInitLocal) {\n    if (\n      expressions.length >= 2 &&\n      isProtoInitCallExpression(expressions[1], protoInitLocal)\n    ) {\n      // Merge `super(), protoInit(this)` into `protoInit(super())`\n      const mergedSuperCall = t.callExpression(t.cloneNode(protoInitLocal), [\n        expressions[0],\n      ]);\n      expressions.splice(0, 2, mergedSuperCall);\n    }\n    // Merge `protoInit(super()), this` into `protoInit(super())`\n    if (\n      expressions.length >= 2 &&\n      t.isThisExpression(expressions[expressions.length - 1]) &&\n      isProtoInitCallExpression(\n        expressions[expressions.length - 2],\n        protoInitLocal,\n      )\n    ) {\n      expressions.splice(expressions.length - 1, 1);\n    }\n  }\n  return maybeSequenceExpression(expressions);\n}\n\n/**\n * Insert expressions immediately after super() and optimize the output if possible.\n * This function will preserve the completion result using the trailing this expression.\n *\n * @param {t.Expression[]} expressions\n * @param {NodePath<t.ClassMethod>} constructorPath\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns\n */\nfunction insertExpressionsAfterSuperCallAndOptimize(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n  protoInitLocal: t.Identifier,\n) {\n  constructorPath.traverse({\n    CallExpression: {\n      exit(path) {\n        if (!path.get(\"callee\").isSuper()) return;\n        const newNodes = [\n          path.node,\n          ...expressions.map(expr => t.cloneNode(expr)),\n        ];\n        // preserve completion result if super() is in an RHS or a return statement\n        if (path.isCompletionRecord()) {\n          newNodes.push(t.thisExpression());\n        }\n        path.replaceWith(\n          optimizeSuperCallAndExpressions(newNodes, protoInitLocal),\n        );\n\n        path.skip();\n      },\n    },\n    ClassMethod(path) {\n      if (path.node.kind === \"constructor\") {\n        path.skip();\n      }\n    },\n  });\n}\n\n/**\n * Build a class constructor node from the given expressions. If the class is\n * derived, the constructor will call super() first to ensure that `this`\n * in the expressions work as expected.\n *\n * @param {t.Expression[]} expressions\n * @param {boolean} isDerivedClass\n * @returns The class constructor node\n */\nfunction createConstructorFromExpressions(\n  expressions: t.Expression[],\n  isDerivedClass: boolean,\n) {\n  const body: t.Statement[] = [\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ];\n  if (isDerivedClass) {\n    body.unshift(\n      t.expressionStatement(\n        t.callExpression(t.super(), [t.spreadElement(t.identifier(\"args\"))]),\n      ),\n    );\n  }\n  return t.classMethod(\n    \"constructor\",\n    t.identifier(\"constructor\"),\n    isDerivedClass ? [t.restElement(t.identifier(\"args\"))] : [],\n    t.blockStatement(body),\n  );\n}\n\nfunction createStaticBlockFromExpressions(expressions: t.Expression[]) {\n  return t.staticBlock([\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ]);\n}\n\n// 3 bits reserved to this (0-7)\nconst FIELD = 0;\nconst ACCESSOR = 1;\nconst METHOD = 2;\nconst GETTER = 3;\nconst SETTER = 4;\n\nconst STATIC_OLD_VERSION = 5; // Before 2023-05\nconst STATIC = 8; // 1 << 3\nconst DECORATORS_HAVE_THIS = 16; // 1 << 4\n\nfunction getElementKind(element: NodePath<ClassDecoratableElement>): number {\n  switch (element.node.type) {\n    case \"ClassProperty\":\n    case \"ClassPrivateProperty\":\n      return FIELD;\n    case \"ClassAccessorProperty\":\n      return ACCESSOR;\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n      if (element.node.kind === \"get\") {\n        return GETTER;\n      } else if (element.node.kind === \"set\") {\n        return SETTER;\n      } else {\n        return METHOD;\n      }\n  }\n}\n\n// Information about the decorators applied to an element\ninterface DecoratorInfo {\n  // An array of applied decorators or a memoised identifier\n  decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n  decoratorsHaveThis: boolean;\n\n  // The kind of the decorated value, matches the kind value passed to applyDecs\n  kind: number;\n\n  // whether or not the field is static\n  isStatic: boolean;\n\n  // The name of the decorator\n  name: t.StringLiteral | t.Expression;\n\n  privateMethods:\n    | (t.FunctionExpression | t.ArrowFunctionExpression)[]\n    | undefined;\n\n  // The names of local variables that will be used/returned from the decoration\n  locals: t.Identifier | t.Identifier[] | undefined;\n}\n\n/**\n * Sort decoration info in the application order:\n * - static non-fields\n * - instance non-fields\n * - static fields\n * - instance fields\n *\n * @param {DecoratorInfo[]} info\n * @returns {DecoratorInfo[]} Sorted decoration info\n */\nfunction toSortedDecoratorInfo(info: DecoratorInfo[]): DecoratorInfo[] {\n  return [\n    ...info.filter(\n      el => el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(\n      el => !el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(el => el.isStatic && el.kind === FIELD),\n    ...info.filter(el => !el.isStatic && el.kind === FIELD),\n  ];\n}\n\ntype GenerateDecorationListResult = {\n  // The zipped decorators array that will be passed to generateDecorationExprs\n  decs: t.Expression[];\n  // Whether there are non-empty decorator this values\n  haveThis: boolean;\n};\n/**\n * Zip decorators and decorator this values into an array\n *\n * @param {t.Decorator[]} decorators\n * @param {((t.Expression | undefined)[])} decoratorsThis decorator this values\n * @param {DecoratorVersionKind} version\n * @returns {GenerateDecorationListResult}\n */\nfunction generateDecorationList(\n  decorators: t.Decorator[],\n  decoratorsThis: (t.Expression | undefined)[],\n  version: DecoratorVersionKind,\n): GenerateDecorationListResult {\n  const decsCount = decorators.length;\n  const haveOneThis = decoratorsThis.some(Boolean);\n  const decs: t.Expression[] = [];\n  for (let i = 0; i < decsCount; i++) {\n    if (\n      (version === \"2023-11\" ||\n        (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n      haveOneThis\n    ) {\n      decs.push(\n        decoratorsThis[i] || t.unaryExpression(\"void\", t.numericLiteral(0)),\n      );\n    }\n    decs.push(decorators[i].expression);\n  }\n\n  return { haveThis: haveOneThis, decs };\n}\n\nfunction generateDecorationExprs(\n  decorationInfo: DecoratorInfo[],\n  version: DecoratorVersionKind,\n): t.ArrayExpression {\n  return t.arrayExpression(\n    decorationInfo.map(el => {\n      let flag = el.kind;\n      if (el.isStatic) {\n        flag +=\n          version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")\n            ? STATIC\n            : STATIC_OLD_VERSION;\n      }\n      if (el.decoratorsHaveThis) flag += DECORATORS_HAVE_THIS;\n\n      return t.arrayExpression([\n        el.decoratorsArray,\n        t.numericLiteral(flag),\n        el.name,\n        ...(el.privateMethods || []),\n      ]);\n    }),\n  );\n}\n\nfunction extractElementLocalAssignments(decorationInfo: DecoratorInfo[]) {\n  const localIds: t.Identifier[] = [];\n\n  for (const el of decorationInfo) {\n    const { locals } = el;\n\n    if (Array.isArray(locals)) {\n      localIds.push(...locals);\n    } else if (locals !== undefined) {\n      localIds.push(locals);\n    }\n  }\n\n  return localIds;\n}\n\nfunction addCallAccessorsFor(\n  version: DecoratorVersionKind,\n  element: NodePath,\n  key: t.PrivateName,\n  getId: t.Identifier,\n  setId: t.Identifier,\n  isStatic: boolean,\n) {\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(key),\n      [],\n      t.blockStatement([\n        t.returnStatement(\n          t.callExpression(\n            t.cloneNode(getId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(key),\n      [t.identifier(\"v\")],\n      t.blockStatement([\n        t.expressionStatement(\n          t.callExpression(\n            t.cloneNode(setId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? [t.identifier(\"v\")]\n              : [t.thisExpression(), t.identifier(\"v\")],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n}\n\nfunction movePrivateAccessor(\n  element: NodePath<t.ClassPrivateMethod>,\n  key: t.PrivateName,\n  methodLocalVar: t.Identifier,\n  isStatic: boolean,\n) {\n  let params: (t.Identifier | t.RestElement)[];\n  let block: t.Statement[];\n\n  if (element.node.kind === \"set\") {\n    params = [t.identifier(\"v\")];\n    block = [\n      t.expressionStatement(\n        t.callExpression(methodLocalVar, [\n          t.thisExpression(),\n          t.identifier(\"v\"),\n        ]),\n      ),\n    ];\n  } else {\n    params = [];\n    block = [\n      t.returnStatement(t.callExpression(methodLocalVar, [t.thisExpression()])),\n    ];\n  }\n\n  element.replaceWith(\n    t.classPrivateMethod(\n      element.node.kind,\n      t.cloneNode(key),\n      params,\n      t.blockStatement(block),\n      isStatic,\n    ),\n  );\n}\n\nfunction isClassDecoratableElementPath(\n  path: NodePath<ClassElement>,\n): path is NodePath<ClassDecoratableElement> {\n  const { type } = path;\n\n  return (\n    type !== \"TSDeclareMethod\" &&\n    type !== \"TSIndexSignature\" &&\n    type !== \"StaticBlock\"\n  );\n}\n\nfunction staticBlockToIIFE(block: t.StaticBlock) {\n  return t.callExpression(\n    t.arrowFunctionExpression([], t.blockStatement(block.body)),\n    [],\n  );\n}\n\nfunction staticBlockToFunctionClosure(block: t.StaticBlock) {\n  return t.functionExpression(null, [], t.blockStatement(block.body));\n}\n\nfunction fieldInitializerToClosure(value: t.Expression) {\n  return t.functionExpression(\n    null,\n    [],\n    t.blockStatement([t.returnStatement(value)]),\n  );\n}\n\nfunction maybeSequenceExpression(exprs: t.Expression[]) {\n  if (exprs.length === 0) return t.unaryExpression(\"void\", t.numericLiteral(0));\n  if (exprs.length === 1) return exprs[0];\n  return t.sequenceExpression(exprs);\n}\n\n/**\n * Create FunctionExpression from a ClassPrivateMethod.\n * The returned FunctionExpression node takes ownership of the private method's body and params.\n *\n * @param {t.ClassPrivateMethod} node\n * @returns\n */\nfunction createFunctionExpressionFromPrivateMethod(node: t.ClassPrivateMethod) {\n  const { params, body, generator: isGenerator, async: isAsync } = node;\n  return t.functionExpression(\n    undefined,\n    // @ts-expect-error todo: Improve typings: TSParameterProperty is only allowed in constructor\n    params,\n    body,\n    isGenerator,\n    isAsync,\n  );\n}\n\nfunction createSetFunctionNameCall(\n  state: PluginPass,\n  className: t.Identifier | t.StringLiteral,\n) {\n  return t.callExpression(state.addHelper(\"setFunctionName\"), [\n    t.thisExpression(),\n    className,\n  ]);\n}\n\nfunction createToPropertyKeyCall(state: PluginPass, propertyKey: t.Expression) {\n  return t.callExpression(state.addHelper(\"toPropertyKey\"), [propertyKey]);\n}\n\nfunction createPrivateBrandCheckClosure(brandName: t.PrivateName) {\n  return t.arrowFunctionExpression(\n    [t.identifier(\"_\")],\n    t.binaryExpression(\"in\", t.cloneNode(brandName), t.identifier(\"_\")),\n  );\n}\n\nfunction usesPrivateField(expression: t.Node) {\n  try {\n    t.traverseFast(expression, node => {\n      if (t.isPrivateName(node)) {\n        // TODO: Add early return support to t.traverseFast\n        // eslint-disable-next-line @typescript-eslint/only-throw-error\n        throw null;\n      }\n    });\n    return false;\n  } catch {\n    return true;\n  }\n}\n\n/**\n * Convert a non-computed class element to its equivalent computed form.\n *\n * This function is to provide a decorator evaluation storage from non-computed\n * class elements.\n *\n * @param {(NodePath<t.ClassProperty | t.ClassMethod>)} path A non-computed class property or method\n */\nfunction convertToComputedKey(path: NodePath<t.ClassProperty | t.ClassMethod>) {\n  const { node } = path;\n  node.computed = true;\n  if (t.isIdentifier(node.key)) {\n    node.key = t.stringLiteral(node.key.name);\n  }\n}\n\nfunction hasInstancePrivateAccess(path: NodePath, privateNames: string[]) {\n  let containsInstancePrivateAccess = false;\n  if (privateNames.length > 0) {\n    const privateNameVisitor = privateNameVisitorFactory<\n      PrivateNameVisitorState<null>,\n      null\n    >({\n      PrivateName(path, state) {\n        if (state.privateNamesMap.has(path.node.id.name)) {\n          containsInstancePrivateAccess = true;\n          path.stop();\n        }\n      },\n    });\n    const privateNamesMap = new Map<string, null>();\n    for (const name of privateNames) {\n      privateNamesMap.set(name, null);\n    }\n    path.traverse(privateNameVisitor, {\n      privateNamesMap: privateNamesMap,\n    });\n  }\n  return containsInstancePrivateAccess;\n}\n\nfunction checkPrivateMethodUpdateError(\n  path: NodePath<t.Class>,\n  decoratedPrivateMethods: Set<string>,\n) {\n  const privateNameVisitor = privateNameVisitorFactory<\n    PrivateNameVisitorState<null>,\n    null\n  >({\n    PrivateName(path, state) {\n      if (!state.privateNamesMap.has(path.node.id.name)) return;\n\n      const parentPath = path.parentPath;\n      const parentParentPath = parentPath.parentPath;\n\n      if (\n        // this.bar().#x = 123;\n        (parentParentPath.node.type === \"AssignmentExpression\" &&\n          parentParentPath.node.left === parentPath.node) ||\n        // this.#x++;\n        parentParentPath.node.type === \"UpdateExpression\" ||\n        // ([...this.#x] = foo);\n        parentParentPath.node.type === \"RestElement\" ||\n        // ([this.#x] = foo);\n        parentParentPath.node.type === \"ArrayPattern\" ||\n        // ({ a: this.#x } = bar);\n        (parentParentPath.node.type === \"ObjectProperty\" &&\n          parentParentPath.node.value === parentPath.node &&\n          parentParentPath.parentPath.type === \"ObjectPattern\") ||\n        // for (this.#x of []);\n        (parentParentPath.node.type === \"ForOfStatement\" &&\n          parentParentPath.node.left === parentPath.node)\n      ) {\n        throw path.buildCodeFrameError(\n          `Decorated private methods are read-only, but \"#${path.node.id.name}\" is updated via this expression.`,\n        );\n      }\n    },\n  });\n  const privateNamesMap = new Map<string, null>();\n  for (const name of decoratedPrivateMethods) {\n    privateNamesMap.set(name, null);\n  }\n  path.traverse(privateNameVisitor, {\n    privateNamesMap: privateNamesMap,\n  });\n}\n\n/**\n * Apply decorator and accessor transform\n * @param path The class path.\n * @param state The plugin pass.\n * @param constantSuper The constantSuper compiler assumption.\n * @param ignoreFunctionLength The ignoreFunctionLength compiler assumption.\n * @param className The class name.\n * - If className is a `string`, it will be a valid identifier name that can safely serve as a class id\n * - If className is an Identifier, it is the reference to the name derived from NamedEvaluation\n * - If className is a StringLiteral, it is derived from NamedEvaluation on literal computed keys\n * @param propertyVisitor The visitor that should be applied on property prior to the transform.\n * @param version The decorator version.\n * @returns The transformed class path or undefined if there are no decorators.\n */\nfunction transformClass(\n  path: NodePath<t.Class>,\n  state: PluginPass,\n  constantSuper: boolean,\n  ignoreFunctionLength: boolean,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n  propertyVisitor: Visitor<PluginPass>,\n  version: DecoratorVersionKind,\n): NodePath | undefined {\n  const body = path.get(\"body.body\");\n\n  const classDecorators = path.node.decorators;\n  let hasElementDecorators = false;\n  let hasComputedKeysSideEffects = false;\n  let elemDecsUseFnContext = false;\n\n  const generateClassPrivateUid = createLazyPrivateUidGeneratorForClass(path);\n\n  const classAssignments: t.AssignmentExpression[] = [];\n  const scopeParent: Scope = path.scope.parent;\n  const memoiseExpression = (\n    expression: t.Expression,\n    hint: string,\n    assignments: t.AssignmentExpression[],\n  ) => {\n    const localEvaluatedId = generateLetUidIdentifier(scopeParent, hint);\n    assignments.push(t.assignmentExpression(\"=\", localEvaluatedId, expression));\n    return t.cloneNode(localEvaluatedId);\n  };\n\n  let protoInitLocal: t.Identifier;\n  let staticInitLocal: t.Identifier;\n  const classIdName = path.node.id?.name;\n  // Whether to generate a setFunctionName call to preserve the class name\n  const setClassName = typeof className === \"object\" ? className : undefined;\n  // Check if the decorator does not reference function-specific\n  // context or the given identifier name or contains yield or await expression.\n  // `true` means \"maybe\" and `false` means \"no\".\n  const usesFunctionContextOrYieldAwait = (decorator: t.Decorator) => {\n    try {\n      t.traverseFast(decorator, node => {\n        if (\n          t.isThisExpression(node) ||\n          t.isSuper(node) ||\n          t.isYieldExpression(node) ||\n          t.isAwaitExpression(node) ||\n          t.isIdentifier(node, { name: \"arguments\" }) ||\n          (classIdName && t.isIdentifier(node, { name: classIdName })) ||\n          (t.isMetaProperty(node) && node.meta.name !== \"import\")\n        ) {\n          // TODO: Add early return support to t.traverseFast\n          // eslint-disable-next-line @typescript-eslint/only-throw-error\n          throw null;\n        }\n      });\n      return false;\n    } catch {\n      return true;\n    }\n  };\n\n  const instancePrivateNames: string[] = [];\n  // Iterate over the class to see if we need to decorate it, and also to\n  // transform simple auto accessors which are not decorated, and handle inferred\n  // class name when the initializer of the class field is a class expression\n  for (const element of body) {\n    if (!isClassDecoratableElementPath(element)) {\n      continue;\n    }\n\n    const elementNode = element.node;\n\n    if (!elementNode.static && t.isPrivateName(elementNode.key)) {\n      instancePrivateNames.push(elementNode.key.id.name);\n    }\n\n    if (isDecorated(elementNode)) {\n      switch (elementNode.type) {\n        case \"ClassProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassProperty should be callable. Improve typings.\n          propertyVisitor.ClassProperty(\n            element as NodePath<t.ClassProperty>,\n            state,\n          );\n          break;\n        case \"ClassPrivateProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassPrivateProperty should be callable. Improve typings.\n          propertyVisitor.ClassPrivateProperty(\n            element as NodePath<t.ClassPrivateProperty>,\n            state,\n          );\n          break;\n        case \"ClassAccessorProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n          propertyVisitor.ClassAccessorProperty(\n            element as NodePath<t.ClassAccessorProperty>,\n            state,\n          );\n          if (version === \"2023-11\") {\n            break;\n          }\n        /* fallthrough */\n        default:\n          if (elementNode.static) {\n            staticInitLocal ??= generateLetUidIdentifier(\n              scopeParent,\n              \"initStatic\",\n            );\n          } else {\n            protoInitLocal ??= generateLetUidIdentifier(\n              scopeParent,\n              \"initProto\",\n            );\n          }\n          break;\n      }\n      hasElementDecorators = true;\n      elemDecsUseFnContext ||= elementNode.decorators.some(\n        usesFunctionContextOrYieldAwait,\n      );\n    } else if (elementNode.type === \"ClassAccessorProperty\") {\n      // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n      propertyVisitor.ClassAccessorProperty(\n        element as NodePath<t.ClassAccessorProperty>,\n        state,\n      );\n      const { key, value, static: isStatic, computed } = elementNode;\n\n      const newId = generateClassPrivateUid();\n      const newField = generateClassProperty(newId, value, isStatic);\n      const keyPath = element.get(\"key\");\n      const [newPath] = element.replaceWith(newField);\n\n      let getterKey, setterKey;\n      if (computed && !keyPath.isConstantExpression()) {\n        getterKey = memoiseComputedKey(\n          createToPropertyKeyCall(state, key as t.Expression),\n          scopeParent,\n          scopeParent.generateUid(\"computedKey\"),\n        )!;\n        setterKey = t.cloneNode(getterKey.left as t.Identifier);\n      } else {\n        getterKey = t.cloneNode(key);\n        setterKey = t.cloneNode(key);\n      }\n\n      assignIdForAnonymousClass(path, className);\n\n      addProxyAccessorsFor(\n        path.node.id,\n        newPath,\n        getterKey,\n        setterKey,\n        newId,\n        computed,\n        isStatic,\n        version,\n      );\n    }\n\n    if (\"computed\" in element.node && element.node.computed) {\n      hasComputedKeysSideEffects ||= !scopeParent.isStatic(element.node.key);\n    }\n  }\n\n  if (!classDecorators && !hasElementDecorators) {\n    if (!path.node.id && typeof className === \"string\") {\n      path.node.id = t.identifier(className);\n    }\n    if (setClassName) {\n      path.node.body.body.unshift(\n        createStaticBlockFromExpressions([\n          createSetFunctionNameCall(state, setClassName),\n        ]),\n      );\n    }\n    // If nothing is decorated and no assignments inserted, return\n    return;\n  }\n\n  const elementDecoratorInfo: DecoratorInfo[] = [];\n\n  let constructorPath: NodePath<t.ClassMethod> | undefined;\n  const decoratedPrivateMethods = new Set<string>();\n\n  let classInitLocal: t.Identifier, classIdLocal: t.Identifier;\n  let decoratorReceiverId: t.Identifier | null = null;\n\n  // Memoise the this value `a.b` of decorator member expressions `@a.b.dec`,\n  type HandleDecoratorsResult = {\n    // whether the whole decorator list requires memoisation\n    hasSideEffects: boolean;\n    usesFnContext: boolean;\n    // the this value of each decorator if applicable\n    decoratorsThis: (t.Expression | undefined)[];\n  };\n  function handleDecorators(decorators: t.Decorator[]): HandleDecoratorsResult {\n    let hasSideEffects = false;\n    let usesFnContext = false;\n    const decoratorsThis: (t.Expression | null)[] = [];\n    for (const decorator of decorators) {\n      const { expression } = decorator;\n      let object;\n      if (\n        (version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n        t.isMemberExpression(expression)\n      ) {\n        if (t.isSuper(expression.object)) {\n          object = t.thisExpression();\n        } else if (scopeParent.isStatic(expression.object)) {\n          object = t.cloneNode(expression.object);\n        } else {\n          decoratorReceiverId ??= generateLetUidIdentifier(scopeParent, \"obj\");\n          object = t.assignmentExpression(\n            \"=\",\n            t.cloneNode(decoratorReceiverId),\n            expression.object,\n          );\n          expression.object = t.cloneNode(decoratorReceiverId);\n        }\n      }\n      decoratorsThis.push(object);\n      hasSideEffects ||= !scopeParent.isStatic(expression);\n      usesFnContext ||= usesFunctionContextOrYieldAwait(decorator);\n    }\n    return { hasSideEffects, usesFnContext, decoratorsThis };\n  }\n\n  const willExtractSomeElemDecs =\n    hasComputedKeysSideEffects ||\n    (process.env.BABEL_8_BREAKING\n      ? elemDecsUseFnContext\n      : elemDecsUseFnContext || version !== \"2023-11\");\n\n  let needsDeclaraionForClassBinding = false;\n  let classDecorationsFlag = 0;\n  let classDecorations: t.Expression[] = [];\n  let classDecorationsId: t.Identifier;\n  let computedKeyAssignments: t.AssignmentExpression[] = [];\n  if (classDecorators) {\n    classInitLocal = generateLetUidIdentifier(scopeParent, \"initClass\");\n    needsDeclaraionForClassBinding = path.isClassDeclaration();\n    ({ id: classIdLocal, path } = replaceClassWithVar(path, className));\n\n    path.node.decorators = null;\n\n    const classDecsUsePrivateName = classDecorators.some(usesPrivateField);\n    const { hasSideEffects, usesFnContext, decoratorsThis } =\n      handleDecorators(classDecorators);\n\n    const { haveThis, decs } = generateDecorationList(\n      classDecorators,\n      decoratorsThis,\n      version,\n    );\n    classDecorationsFlag = haveThis ? 1 : 0;\n    classDecorations = decs;\n\n    if (\n      usesFnContext ||\n      (hasSideEffects && willExtractSomeElemDecs) ||\n      classDecsUsePrivateName\n    ) {\n      classDecorationsId = memoiseExpression(\n        t.arrayExpression(classDecorations),\n        \"classDecs\",\n        classAssignments,\n      );\n    }\n\n    if (!hasElementDecorators) {\n      // Sync body paths as non-decorated computed accessors have been transpiled\n      // to getter-setter pairs.\n      for (const element of path.get(\"body.body\")) {\n        const { node } = element;\n        const isComputed = \"computed\" in node && node.computed;\n        if (isComputed) {\n          if (element.isClassProperty({ static: true })) {\n            if (!element.get(\"key\").isConstantExpression()) {\n              const key = (node as t.ClassProperty).key;\n              const maybeAssignment = memoiseComputedKey(\n                key,\n                scopeParent,\n                scopeParent.generateUid(\"computedKey\"),\n              );\n              if (maybeAssignment != null) {\n                // If it is a static computed field within a decorated class, we move the computed key\n                // into `computedKeyAssignments` which will be then moved into the non-static class,\n                // to ensure that the evaluation order and private environment are correct\n                node.key = t.cloneNode(maybeAssignment.left);\n                computedKeyAssignments.push(maybeAssignment);\n              }\n            }\n          } else if (computedKeyAssignments.length > 0) {\n            prependExpressionsToComputedKey(\n              computedKeyAssignments,\n              element as NodePath<ClassElementCanHaveComputedKeys>,\n            );\n            computedKeyAssignments = [];\n          }\n        }\n      }\n    }\n  } else {\n    assignIdForAnonymousClass(path, className);\n    classIdLocal = t.cloneNode(path.node.id);\n  }\n\n  let lastInstancePrivateName: t.PrivateName;\n  let needsInstancePrivateBrandCheck = false;\n\n  let fieldInitializerExpressions = [];\n  let staticFieldInitializerExpressions: t.Expression[] = [];\n\n  if (hasElementDecorators) {\n    if (protoInitLocal) {\n      const protoInitCall = t.callExpression(t.cloneNode(protoInitLocal), [\n        t.thisExpression(),\n      ]);\n      fieldInitializerExpressions.push(protoInitCall);\n    }\n    for (const element of body) {\n      if (!isClassDecoratableElementPath(element)) {\n        if (\n          staticFieldInitializerExpressions.length > 0 &&\n          element.isStaticBlock()\n        ) {\n          prependExpressionsToStaticBlock(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n        continue;\n      }\n\n      const { node } = element;\n      const decorators = node.decorators;\n\n      const hasDecorators = !!decorators?.length;\n\n      const isComputed = \"computed\" in node && node.computed;\n\n      let name = \"computedKey\";\n\n      if (node.key.type === \"PrivateName\") {\n        name = node.key.id.name;\n      } else if (!isComputed && node.key.type === \"Identifier\") {\n        name = node.key.name;\n      }\n      let decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n      let decoratorsHaveThis;\n\n      if (hasDecorators) {\n        const { hasSideEffects, usesFnContext, decoratorsThis } =\n          handleDecorators(decorators);\n        const { decs, haveThis } = generateDecorationList(\n          decorators,\n          decoratorsThis,\n          version,\n        );\n        decoratorsHaveThis = haveThis;\n        decoratorsArray = decs.length === 1 ? decs[0] : t.arrayExpression(decs);\n        if (usesFnContext || (hasSideEffects && willExtractSomeElemDecs)) {\n          decoratorsArray = memoiseExpression(\n            decoratorsArray,\n            name + \"Decs\",\n            computedKeyAssignments,\n          );\n        }\n      }\n\n      if (isComputed) {\n        if (!element.get(\"key\").isConstantExpression()) {\n          const key = node.key as t.Expression;\n          const maybeAssignment = memoiseComputedKey(\n            hasDecorators ? createToPropertyKeyCall(state, key) : key,\n            scopeParent,\n            scopeParent.generateUid(\"computedKey\"),\n          );\n          if (maybeAssignment != null) {\n            // If it is a static computed field within a decorated class, we move the computed key\n            // into `computedKeyAssignments` which will be then moved into the non-static class,\n            // to ensure that the evaluation order and private environment are correct\n            if (classDecorators && element.isClassProperty({ static: true })) {\n              node.key = t.cloneNode(maybeAssignment.left);\n              computedKeyAssignments.push(maybeAssignment);\n            } else {\n              node.key = maybeAssignment;\n            }\n          }\n        }\n      }\n\n      const { key, static: isStatic } = node;\n\n      const isPrivate = key.type === \"PrivateName\";\n\n      const kind = getElementKind(element);\n\n      if (isPrivate && !isStatic) {\n        if (hasDecorators) {\n          needsInstancePrivateBrandCheck = true;\n        }\n        if (t.isClassPrivateProperty(node) || !lastInstancePrivateName) {\n          lastInstancePrivateName = key;\n        }\n      }\n\n      if (element.isClassMethod({ kind: \"constructor\" })) {\n        constructorPath = element;\n      }\n\n      let locals: t.Identifier[];\n      if (hasDecorators) {\n        let privateMethods: Array<\n          t.FunctionExpression | t.ArrowFunctionExpression\n        >;\n\n        let nameExpr: t.Expression;\n\n        if (isComputed) {\n          nameExpr = getComputedKeyMemoiser(\n            element.get(\"key\") as NodePath<t.Expression>,\n          );\n        } else if (key.type === \"PrivateName\") {\n          nameExpr = t.stringLiteral(key.id.name);\n        } else if (key.type === \"Identifier\") {\n          nameExpr = t.stringLiteral(key.name);\n        } else {\n          nameExpr = t.cloneNode(key as t.Expression);\n        }\n\n        if (kind === ACCESSOR) {\n          const { value } = element.node as t.ClassAccessorProperty;\n\n          const params: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n\n          if (value) {\n            params.push(t.cloneNode(value));\n          }\n\n          const newId = generateClassPrivateUid();\n          const newFieldInitId = generateLetUidIdentifier(\n            scopeParent,\n            `init_${name}`,\n          );\n          const newValue = t.callExpression(\n            t.cloneNode(newFieldInitId),\n            params,\n          );\n\n          const newField = generateClassProperty(newId, newValue, isStatic);\n          const [newPath] = element.replaceWith(newField);\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(newId, version);\n\n            const getId = generateLetUidIdentifier(scopeParent, `get_${name}`);\n            const setId = generateLetUidIdentifier(scopeParent, `set_${name}`);\n\n            addCallAccessorsFor(version, newPath, key, getId, setId, isStatic);\n\n            locals = [newFieldInitId, getId, setId];\n          } else {\n            assignIdForAnonymousClass(path, className);\n            addProxyAccessorsFor(\n              path.node.id,\n              newPath,\n              t.cloneNode(key),\n              t.isAssignmentExpression(key)\n                ? t.cloneNode(key.left as t.Identifier)\n                : t.cloneNode(key),\n              newId,\n              isComputed,\n              isStatic,\n              version,\n            );\n            locals = [newFieldInitId];\n          }\n        } else if (kind === FIELD) {\n          const initId = generateLetUidIdentifier(scopeParent, `init_${name}`);\n          const valuePath = (\n            element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n          ).get(\"value\");\n\n          const args: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n          if (valuePath.node) args.push(valuePath.node);\n\n          valuePath.replaceWith(t.callExpression(t.cloneNode(initId), args));\n\n          locals = [initId];\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(key, version);\n          }\n        } else if (isPrivate) {\n          const callId = generateLetUidIdentifier(scopeParent, `call_${name}`);\n          locals = [callId];\n\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element as NodePath<t.ClassPrivateMethod>,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          privateMethods = [\n            createFunctionExpressionFromPrivateMethod(\n              element.node as t.ClassPrivateMethod,\n            ),\n          ];\n\n          if (kind === GETTER || kind === SETTER) {\n            movePrivateAccessor(\n              element as NodePath<t.ClassPrivateMethod>,\n              t.cloneNode(key),\n              t.cloneNode(callId),\n              isStatic,\n            );\n          } else {\n            const node = element.node as t.ClassPrivateMethod;\n\n            // Unshift\n            path.node.body.body.unshift(\n              t.classPrivateProperty(key, t.cloneNode(callId), [], node.static),\n            );\n\n            decoratedPrivateMethods.add(key.id.name);\n\n            element.remove();\n          }\n        }\n\n        elementDecoratorInfo.push({\n          kind,\n          decoratorsArray,\n          decoratorsHaveThis,\n          name: nameExpr,\n          isStatic,\n          privateMethods,\n          locals,\n        });\n\n        if (element.node) {\n          element.node.decorators = null;\n        }\n      }\n\n      if (isComputed && computedKeyAssignments.length > 0) {\n        if (classDecorators && element.isClassProperty({ static: true })) {\n          // If the class is decorated, we don't insert computedKeyAssignments here\n          // because any non-static computed elements defined after it will be moved\n          // into the non-static class, so they will be evaluated before the key of\n          // this field. At this momemnt, its key must be either a constant expression\n          // or a uid reference which has been assigned _within_ the non-static class.\n        } else {\n          prependExpressionsToComputedKey(\n            computedKeyAssignments,\n            (kind === ACCESSOR\n              ? element.getNextSibling() // the transpiled getter of the accessor property\n              : element) as NodePath<ClassElementCanHaveComputedKeys>,\n          );\n          computedKeyAssignments = [];\n        }\n      }\n\n      if (\n        fieldInitializerExpressions.length > 0 &&\n        !isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          fieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        fieldInitializerExpressions = [];\n      }\n\n      if (\n        staticFieldInitializerExpressions.length > 0 &&\n        isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          staticFieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        staticFieldInitializerExpressions = [];\n      }\n\n      if (hasDecorators && version === \"2023-11\") {\n        if (kind === FIELD || kind === ACCESSOR) {\n          const initExtraId = generateLetUidIdentifier(\n            scopeParent,\n            `init_extra_${name}`,\n          );\n          locals.push(initExtraId);\n          const initExtraCall = t.callExpression(\n            t.cloneNode(initExtraId),\n            isStatic ? [] : [t.thisExpression()],\n          );\n          if (!isStatic) {\n            fieldInitializerExpressions.push(initExtraCall);\n          } else {\n            staticFieldInitializerExpressions.push(initExtraCall);\n          }\n        }\n      }\n    }\n  }\n\n  if (computedKeyAssignments.length > 0) {\n    const elements = path.get(\"body.body\");\n    let lastComputedElement: NodePath<ClassElementCanHaveComputedKeys>;\n    for (let i = elements.length - 1; i >= 0; i--) {\n      const path = elements[i];\n      const node = path.node as ClassElementCanHaveComputedKeys;\n      if (node.computed) {\n        if (classDecorators && t.isClassProperty(node, { static: true })) {\n          continue;\n        }\n        lastComputedElement = path as NodePath<ClassElementCanHaveComputedKeys>;\n        break;\n      }\n    }\n    if (lastComputedElement != null) {\n      appendExpressionsToComputedKey(\n        computedKeyAssignments,\n        lastComputedElement,\n      );\n      computedKeyAssignments = [];\n    } else {\n      // If there is no computed key, we will try to convert the first non-computed\n      // class element into a computed key and insert assignments there. This will\n      // be done after we handle the class elements split when the class is decorated.\n    }\n  }\n\n  if (fieldInitializerExpressions.length > 0) {\n    const isDerivedClass = !!path.node.superClass;\n    if (constructorPath) {\n      if (isDerivedClass) {\n        insertExpressionsAfterSuperCallAndOptimize(\n          fieldInitializerExpressions,\n          constructorPath,\n          protoInitLocal,\n        );\n      } else {\n        prependExpressionsToConstructor(\n          fieldInitializerExpressions,\n          constructorPath,\n        );\n      }\n    } else {\n      path.node.body.body.unshift(\n        createConstructorFromExpressions(\n          fieldInitializerExpressions,\n          isDerivedClass,\n        ),\n      );\n    }\n    fieldInitializerExpressions = [];\n  }\n\n  if (staticFieldInitializerExpressions.length > 0) {\n    path.node.body.body.push(\n      createStaticBlockFromExpressions(staticFieldInitializerExpressions),\n    );\n    staticFieldInitializerExpressions = [];\n  }\n\n  const sortedElementDecoratorInfo =\n    toSortedDecoratorInfo(elementDecoratorInfo);\n\n  const elementDecorations = generateDecorationExprs(\n    process.env.BABEL_8_BREAKING || version === \"2023-11\"\n      ? elementDecoratorInfo\n      : sortedElementDecoratorInfo,\n    version,\n  );\n\n  const elementLocals: t.Identifier[] = extractElementLocalAssignments(\n    sortedElementDecoratorInfo,\n  );\n\n  if (protoInitLocal) {\n    elementLocals.push(protoInitLocal);\n  }\n\n  if (staticInitLocal) {\n    elementLocals.push(staticInitLocal);\n  }\n\n  const classLocals: t.Identifier[] = [];\n  let classInitInjected = false;\n  const classInitCall =\n    classInitLocal && t.callExpression(t.cloneNode(classInitLocal), []);\n\n  let originalClassPath = path;\n  const originalClass = path.node;\n\n  const staticClosures: t.AssignmentExpression[] = [];\n  if (classDecorators) {\n    classLocals.push(classIdLocal, classInitLocal);\n    const statics: (\n      | t.ClassProperty\n      | t.ClassPrivateProperty\n      | t.ClassPrivateMethod\n    )[] = [];\n    path.get(\"body.body\").forEach(element => {\n      // Static blocks cannot be compiled to \"instance blocks\", but we can inline\n      // them as IIFEs in the next property.\n      if (element.isStaticBlock()) {\n        if (hasInstancePrivateAccess(element, instancePrivateNames)) {\n          const staticBlockClosureId = memoiseExpression(\n            staticBlockToFunctionClosure(element.node),\n            \"staticBlock\",\n            staticClosures,\n          );\n          staticFieldInitializerExpressions.push(\n            t.callExpression(\n              t.memberExpression(staticBlockClosureId, t.identifier(\"call\")),\n              [t.thisExpression()],\n            ),\n          );\n        } else {\n          staticFieldInitializerExpressions.push(\n            staticBlockToIIFE(element.node),\n          );\n        }\n        element.remove();\n        return;\n      }\n\n      if (\n        (element.isClassProperty() || element.isClassPrivateProperty()) &&\n        element.node.static\n      ) {\n        const valuePath = (\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n        ).get(\"value\");\n        if (hasInstancePrivateAccess(valuePath, instancePrivateNames)) {\n          const fieldValueClosureId = memoiseExpression(\n            fieldInitializerToClosure(valuePath.node),\n            \"fieldValue\",\n            staticClosures,\n          );\n          valuePath.replaceWith(\n            t.callExpression(\n              t.memberExpression(fieldValueClosureId, t.identifier(\"call\")),\n              [t.thisExpression()],\n            ),\n          );\n        }\n        if (staticFieldInitializerExpressions.length > 0) {\n          prependExpressionsToFieldInitializer(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      } else if (element.isClassPrivateMethod({ static: true })) {\n        // At this moment the element must not have decorators, so any private name\n        // within the element must come from either params or body\n        if (hasInstancePrivateAccess(element, instancePrivateNames)) {\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          const privateMethodDelegateId = memoiseExpression(\n            createFunctionExpressionFromPrivateMethod(element.node),\n            element.get(\"key.id\").node.name,\n            staticClosures,\n          );\n\n          if (ignoreFunctionLength) {\n            element.node.params = [t.restElement(t.identifier(\"arg\"))];\n            element.node.body = t.blockStatement([\n              t.returnStatement(\n                t.callExpression(\n                  t.memberExpression(\n                    privateMethodDelegateId,\n                    t.identifier(\"apply\"),\n                  ),\n                  [t.thisExpression(), t.identifier(\"arg\")],\n                ),\n              ),\n            ]);\n          } else {\n            element.node.params = element.node.params.map((p, i) => {\n              if (t.isRestElement(p)) {\n                return t.restElement(t.identifier(\"arg\"));\n              } else {\n                return t.identifier(\"_\" + i);\n              }\n            });\n            element.node.body = t.blockStatement([\n              t.returnStatement(\n                t.callExpression(\n                  t.memberExpression(\n                    privateMethodDelegateId,\n                    t.identifier(\"apply\"),\n                  ),\n                  [t.thisExpression(), t.identifier(\"arguments\")],\n                ),\n              ),\n            ]);\n          }\n        }\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      }\n    });\n\n    if (statics.length > 0 || staticFieldInitializerExpressions.length > 0) {\n      const staticsClass = template.expression.ast`\n        class extends ${state.addHelper(\"identity\")} {}\n      ` as t.ClassExpression;\n      staticsClass.body.body = [\n        // Insert the original class to a computed key of the wrapper so that\n        // 1) they share the same function context with the wrapper class\n        // 2) the memoisation of static computed field is evaluated before they\n        //    are referenced in the wrapper class keys\n        // Note that any static elements of the wrapper class can not be accessed\n        // in the user land, so we don't have to remove the temporary class field.\n        t.classProperty(\n          t.toExpression(originalClass),\n          undefined,\n          undefined,\n          undefined,\n          /* computed */ true,\n          /* static */ true,\n        ),\n        ...statics,\n      ];\n\n      const constructorBody: t.Expression[] = [];\n\n      const newExpr = t.newExpression(staticsClass, []);\n\n      if (staticFieldInitializerExpressions.length > 0) {\n        constructorBody.push(...staticFieldInitializerExpressions);\n      }\n      if (classInitCall) {\n        classInitInjected = true;\n        constructorBody.push(classInitCall);\n      }\n      if (constructorBody.length > 0) {\n        constructorBody.unshift(\n          t.callExpression(t.super(), [t.cloneNode(classIdLocal)]),\n        );\n\n        // set isDerivedClass to false as we have already prepended super call\n        staticsClass.body.body.push(\n          createConstructorFromExpressions(\n            constructorBody,\n            /* isDerivedClass */ false,\n          ),\n        );\n      } else {\n        newExpr.arguments.push(t.cloneNode(classIdLocal));\n      }\n\n      const [newPath] = path.replaceWith(newExpr);\n\n      // update originalClassPath according to the new AST\n      originalClassPath = (\n        newPath.get(\"callee\").get(\"body\") as NodePath<t.Class>\n      ).get(\"body.0.key\");\n    }\n  }\n  if (!classInitInjected && classInitCall) {\n    path.node.body.body.push(\n      t.staticBlock([t.expressionStatement(classInitCall)]),\n    );\n  }\n\n  let { superClass } = originalClass;\n  if (\n    superClass &&\n    (process.env.BABEL_8_BREAKING ||\n      version === \"2023-11\" ||\n      version === \"2023-05\")\n  ) {\n    const id = path.scope.maybeGenerateMemoised(superClass);\n    if (id) {\n      originalClass.superClass = t.assignmentExpression(\"=\", id, superClass);\n      superClass = id;\n    }\n  }\n\n  const applyDecoratorWrapper = t.staticBlock([]);\n  originalClass.body.body.unshift(applyDecoratorWrapper);\n  const applyDecsBody = applyDecoratorWrapper.body;\n  if (computedKeyAssignments.length > 0) {\n    const elements = originalClassPath.get(\"body.body\");\n    let firstPublicElement: NodePath<t.ClassProperty | t.ClassMethod>;\n    for (const path of elements) {\n      if (\n        (path.isClassProperty() || path.isClassMethod()) &&\n        (path.node as t.ClassMethod).kind !== \"constructor\"\n      ) {\n        firstPublicElement = path;\n        break;\n      }\n    }\n    if (firstPublicElement != null) {\n      // Convert its key to a computed one to host the decorator evaluations.\n      convertToComputedKey(firstPublicElement);\n      prependExpressionsToComputedKey(\n        computedKeyAssignments,\n        firstPublicElement,\n      );\n    } else {\n      // When there is no public class elements, we inject a temporary computed\n      // field whose key will host the decorator evaluations. The field will be\n      // deleted immediately after it is defiend.\n      originalClass.body.body.unshift(\n        t.classProperty(\n          t.sequenceExpression([\n            ...computedKeyAssignments,\n            t.stringLiteral(\"_\"),\n          ]),\n          undefined,\n          undefined,\n          undefined,\n          /* computed */ true,\n          /* static */ true,\n        ),\n      );\n      applyDecsBody.push(\n        t.expressionStatement(\n          t.unaryExpression(\n            \"delete\",\n            t.memberExpression(t.thisExpression(), t.identifier(\"_\")),\n          ),\n        ),\n      );\n    }\n    computedKeyAssignments = [];\n  }\n\n  applyDecsBody.push(\n    t.expressionStatement(\n      createLocalsAssignment(\n        elementLocals,\n        classLocals,\n        elementDecorations,\n        classDecorationsId ?? t.arrayExpression(classDecorations),\n        t.numericLiteral(classDecorationsFlag),\n        needsInstancePrivateBrandCheck ? lastInstancePrivateName : null,\n        setClassName,\n        t.cloneNode(superClass),\n        state,\n        version,\n      ),\n    ),\n  );\n  if (staticInitLocal) {\n    applyDecsBody.push(\n      t.expressionStatement(\n        t.callExpression(t.cloneNode(staticInitLocal), [t.thisExpression()]),\n      ),\n    );\n  }\n  if (staticClosures.length > 0) {\n    applyDecsBody.push(\n      ...staticClosures.map(expr => t.expressionStatement(expr)),\n    );\n  }\n\n  // When path is a ClassExpression, path.insertBefore will convert `path`\n  // into a SequenceExpression\n  path.insertBefore(classAssignments.map(expr => t.expressionStatement(expr)));\n\n  if (needsDeclaraionForClassBinding) {\n    const classBindingInfo = scopeParent.getBinding(classIdLocal.name);\n    if (!classBindingInfo.constantViolations.length) {\n      // optimization: reuse the inner class binding if the outer class binding is not mutated\n      path.insertBefore(\n        t.variableDeclaration(\"let\", [\n          t.variableDeclarator(t.cloneNode(classIdLocal)),\n        ]),\n      );\n    } else {\n      const classOuterBindingDelegateLocal = scopeParent.generateUidIdentifier(\n        \"t\" + classIdLocal.name,\n      );\n      const classOuterBindingLocal = classIdLocal;\n      path.replaceWithMultiple([\n        t.variableDeclaration(\"let\", [\n          t.variableDeclarator(t.cloneNode(classOuterBindingLocal)),\n          t.variableDeclarator(classOuterBindingDelegateLocal),\n        ]),\n        t.blockStatement([\n          t.variableDeclaration(\"let\", [\n            t.variableDeclarator(t.cloneNode(classIdLocal)),\n          ]),\n          // needsDeclaraionForClassBinding is true ↔ node is a class declaration\n          path.node as t.ClassDeclaration,\n          t.expressionStatement(\n            t.assignmentExpression(\n              \"=\",\n              t.cloneNode(classOuterBindingDelegateLocal),\n              t.cloneNode(classIdLocal),\n            ),\n          ),\n        ]),\n        t.expressionStatement(\n          t.assignmentExpression(\n            \"=\",\n            t.cloneNode(classOuterBindingLocal),\n            t.cloneNode(classOuterBindingDelegateLocal),\n          ),\n        ),\n      ]);\n    }\n  }\n\n  if (decoratedPrivateMethods.size > 0) {\n    checkPrivateMethodUpdateError(path, decoratedPrivateMethods);\n  }\n\n  // Recrawl the scope to make sure new identifiers are properly synced\n  path.scope.crawl();\n\n  return path;\n}\n\nfunction createLocalsAssignment(\n  elementLocals: t.Identifier[],\n  classLocals: t.Identifier[],\n  elementDecorations: t.ArrayExpression | t.Identifier,\n  classDecorations: t.ArrayExpression | t.Identifier,\n  classDecorationsFlag: t.NumericLiteral,\n  maybePrivateBrandName: t.PrivateName | null,\n  setClassName: t.Identifier | t.StringLiteral | undefined,\n  superClass: null | t.Expression,\n  state: PluginPass,\n  version: DecoratorVersionKind,\n) {\n  let lhs, rhs;\n  const args: t.Expression[] = [\n    setClassName\n      ? createSetFunctionNameCall(state, setClassName)\n      : t.thisExpression(),\n    classDecorations,\n    elementDecorations,\n  ];\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (version !== \"2023-11\") {\n      args.splice(1, 2, elementDecorations, classDecorations);\n    }\n    if (\n      version === \"2021-12\" ||\n      (version === \"2022-03\" && !state.availableHelper(\"applyDecs2203R\"))\n    ) {\n      lhs = t.arrayPattern([...elementLocals, ...classLocals]);\n      rhs = t.callExpression(\n        state.addHelper(version === \"2021-12\" ? \"applyDecs\" : \"applyDecs2203\"),\n        args,\n      );\n      return t.assignmentExpression(\"=\", lhs, rhs);\n    } else if (version === \"2022-03\") {\n      rhs = t.callExpression(state.addHelper(\"applyDecs2203R\"), args);\n    } else if (version === \"2023-01\") {\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      }\n      rhs = t.callExpression(state.addHelper(\"applyDecs2301\"), args);\n    } else if (version === \"2023-05\") {\n      if (\n        maybePrivateBrandName ||\n        superClass ||\n        classDecorationsFlag.value !== 0\n      ) {\n        args.push(classDecorationsFlag);\n      }\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      } else if (superClass) {\n        args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n      }\n      if (superClass) args.push(superClass);\n      rhs = t.callExpression(state.addHelper(\"applyDecs2305\"), args);\n    }\n  }\n  if (process.env.BABEL_8_BREAKING || version === \"2023-11\") {\n    if (\n      maybePrivateBrandName ||\n      superClass ||\n      classDecorationsFlag.value !== 0\n    ) {\n      args.push(classDecorationsFlag);\n    }\n    if (maybePrivateBrandName) {\n      args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n    } else if (superClass) {\n      args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n    }\n    if (superClass) args.push(superClass);\n    rhs = t.callExpression(state.addHelper(\"applyDecs2311\"), args);\n  }\n\n  // optimize `{ c: [classLocals] } = applyDecsHelper(...)` to\n  // `[classLocals] = applyDecsHelper(...).c`\n  if (elementLocals.length > 0) {\n    if (classLocals.length > 0) {\n      lhs = t.objectPattern([\n        t.objectProperty(t.identifier(\"e\"), t.arrayPattern(elementLocals)),\n        t.objectProperty(t.identifier(\"c\"), t.arrayPattern(classLocals)),\n      ]);\n    } else {\n      lhs = t.arrayPattern(elementLocals);\n      // @ts-ignore(Babel 7 vs Babel 8) optional removed in Babel 8\n      rhs = t.memberExpression(rhs, t.identifier(\"e\"), false, false);\n    }\n  } else {\n    // invariant: classLocals.length > 0\n    lhs = t.arrayPattern(classLocals);\n    // @ts-ignore(Babel 7 vs Babel 8) optional removed in Babel 8\n    rhs = t.memberExpression(rhs, t.identifier(\"c\"), false, false);\n  }\n\n  return t.assignmentExpression(\"=\", lhs, rhs);\n}\n\nfunction isProtoKey(\n  node: t.Identifier | t.StringLiteral | t.BigIntLiteral | t.NumericLiteral,\n) {\n  return node.type === \"Identifier\"\n    ? node.name === \"__proto__\"\n    : node.value === \"__proto__\";\n}\n\nfunction isDecorated(node: t.Class | ClassDecoratableElement) {\n  return node.decorators && node.decorators.length > 0;\n}\n\nfunction shouldTransformElement(node: ClassElement) {\n  switch (node.type) {\n    case \"ClassAccessorProperty\":\n      return true;\n    case \"ClassMethod\":\n    case \"ClassProperty\":\n    case \"ClassPrivateMethod\":\n    case \"ClassPrivateProperty\":\n      return isDecorated(node);\n    default:\n      return false;\n  }\n}\n\nfunction shouldTransformClass(node: t.Class) {\n  return isDecorated(node) || node.body.body.some(shouldTransformElement);\n}\n\n// Todo: unify name references logic with helper-function-name\nfunction NamedEvaluationVisitoryFactory(\n  isAnonymous: (path: NodePath) => boolean,\n  visitor: (\n    path: NodePath,\n    state: PluginPass,\n    name:\n      | string\n      | t.Identifier\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral,\n  ) => void,\n) {\n  function handleComputedProperty(\n    propertyPath: NodePath<\n      t.ObjectProperty | t.ClassProperty | t.ClassAccessorProperty\n    >,\n    key: t.Expression,\n    state: PluginPass,\n  ): t.StringLiteral | t.Identifier {\n    switch (key.type) {\n      case \"StringLiteral\":\n        return t.stringLiteral(key.value);\n      case \"NumericLiteral\":\n      case \"BigIntLiteral\": {\n        const keyValue = key.value + \"\";\n        propertyPath.get(\"key\").replaceWith(t.stringLiteral(keyValue));\n        return t.stringLiteral(keyValue);\n      }\n      default: {\n        const ref = propertyPath.scope.maybeGenerateMemoised(key);\n        propertyPath\n          .get(\"key\")\n          .replaceWith(\n            t.assignmentExpression(\n              \"=\",\n              ref,\n              createToPropertyKeyCall(state, key),\n            ),\n          );\n        return t.cloneNode(ref);\n      }\n    }\n  }\n  return {\n    VariableDeclarator(path, state) {\n      const id = path.node.id;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"init\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    AssignmentExpression(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          switch (path.node.operator) {\n            case \"=\":\n            case \"&&=\":\n            case \"||=\":\n            case \"??=\":\n              visitor(initializer, state, id.name);\n          }\n        }\n      }\n    },\n    AssignmentPattern(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    // We listen on ObjectExpression so that we don't have to visit\n    // the object properties under object patterns\n    ObjectExpression(path, state) {\n      for (const propertyPath of path.get(\"properties\")) {\n        if (!propertyPath.isObjectProperty()) continue;\n        const { node } = propertyPath;\n        const id = node.key;\n        const initializer = skipTransparentExprWrappers(\n          propertyPath.get(\"value\") as NodePath<t.Expression>,\n        );\n        if (isAnonymous(initializer)) {\n          if (!node.computed) {\n            // ******** RS: PropertyDefinitionEvaluation\n            if (!isProtoKey(id as t.StringLiteral | t.Identifier)) {\n              if (id.type === \"Identifier\") {\n                visitor(initializer, state, id.name);\n              } else {\n                const className = t.stringLiteral(\n                  (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                    .value + \"\",\n                );\n                visitor(initializer, state, className);\n              }\n            }\n          } else {\n            const ref = handleComputedProperty(\n              propertyPath,\n              // The key of a computed object property must not be a private name\n              id as t.Expression,\n              state,\n            );\n            visitor(initializer, state, ref);\n          }\n        }\n      }\n    },\n    ClassPrivateProperty(path, state) {\n      const { node } = path;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        const className = t.stringLiteral(\"#\" + node.key.id.name);\n        visitor(initializer, state, className);\n      }\n    },\n    ClassAccessorProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else if (id.type === \"PrivateName\") {\n            const className = t.stringLiteral(\"#\" + id.id.name);\n            visitor(initializer, state, className);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(\n            path,\n            // The key of a computed accessor property must not be a private name\n            id as t.Expression,\n            state,\n          );\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n    ClassProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(path, id, state);\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n  } satisfies Visitor<PluginPass>;\n}\n\nfunction isDecoratedAnonymousClassExpression(path: NodePath) {\n  return (\n    path.isClassExpression({ id: null }) && shouldTransformClass(path.node)\n  );\n}\n\nfunction generateLetUidIdentifier(scope: Scope, name: string) {\n  const id = scope.generateUidIdentifier(name);\n  scope.push({ id, kind: \"let\" });\n  return t.cloneNode(id);\n}\n\nexport default function (\n  { assertVersion, assumption }: PluginAPI,\n  { loose }: Options,\n  version: DecoratorVersionKind,\n  inherits: PluginObject[\"inherits\"],\n): PluginObject {\n  if (process.env.BABEL_8_BREAKING) {\n    assertVersion(REQUIRED_VERSION(\"^7.21.0\"));\n  } else {\n    if (\n      version === \"2023-11\" ||\n      version === \"2023-05\" ||\n      version === \"2023-01\"\n    ) {\n      assertVersion(REQUIRED_VERSION(\"^7.21.0\"));\n    } else if (version === \"2021-12\") {\n      assertVersion(REQUIRED_VERSION(\"^7.16.0\"));\n    } else {\n      assertVersion(REQUIRED_VERSION(\"^7.19.0\"));\n    }\n  }\n\n  const VISITED = new WeakSet<NodePath>();\n  const constantSuper = assumption(\"constantSuper\") ?? loose;\n  const ignoreFunctionLength = assumption(\"ignoreFunctionLength\") ?? loose;\n\n  const namedEvaluationVisitor: Visitor<PluginPass> =\n    NamedEvaluationVisitoryFactory(\n      isDecoratedAnonymousClassExpression,\n      visitClass,\n    );\n\n  function visitClass(\n    path: NodePath<t.Class>,\n    state: PluginPass,\n    className: string | t.Identifier | t.StringLiteral | undefined,\n  ) {\n    if (VISITED.has(path)) return;\n    const { node } = path;\n    className ??= node.id?.name;\n    const newPath = transformClass(\n      path,\n      state,\n      constantSuper,\n      ignoreFunctionLength,\n      className,\n      namedEvaluationVisitor,\n      version,\n    );\n    if (newPath) {\n      VISITED.add(newPath);\n      return;\n    }\n    VISITED.add(path);\n  }\n\n  return {\n    name: \"proposal-decorators\",\n    inherits: inherits,\n\n    visitor: {\n      ExportDefaultDeclaration(path, state) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          const isAnonymous = !declaration.id;\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.splitExportDeclaration ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n          }\n          const updatedVarDeclarationPath =\n            path.splitExportDeclaration() as NodePath<t.ClassDeclaration>;\n          if (isAnonymous) {\n            visitClass(\n              updatedVarDeclarationPath,\n              state,\n              t.stringLiteral(\"default\"),\n            );\n          }\n        }\n      },\n      ExportNamedDeclaration(path) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.splitExportDeclaration ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n          }\n          path.splitExportDeclaration();\n        }\n      },\n\n      Class(path, state) {\n        visitClass(path, state, undefined);\n      },\n\n      ...namedEvaluationVisitor,\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AAEA,IAAAE,wCAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAIA,IAAAI,KAAA,GAAAJ,OAAA;AAoCA,SAASK,WAAWA,CAACC,EAAY,EAAEC,GAAG,GAAGD,EAAE,CAACE,MAAM,GAAG,CAAC,EAAQ;EAE5D,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE;IACdD,EAAE,CAACG,OAAO,GAAqB,CAAC;IAChC;EACF;EAEA,MAAMC,OAAO,GAAGJ,EAAE,CAACC,GAAG,CAAC;EAEvB,IAAIG,OAAO,OAAyB,EAAE;IAEpCJ,EAAE,CAACC,GAAG,CAAC,KAAuB;EAChC,CAAC,MAAM,IAAIG,OAAO,QAAyB,EAAE;IAE3CJ,EAAE,CAACC,GAAG,CAAC,KAAuB;IAC9BF,WAAW,CAACC,EAAE,EAAEC,GAAG,GAAG,CAAC,CAAC;EAC1B,CAAC,MAAM;IAELD,EAAE,CAACC,GAAG,CAAC,GAAGG,OAAO,GAAG,CAAC;EACvB;AACF;AASA,SAASC,iCAAiCA,CACxCC,SAA2D,EACtC;EACrB,MAAMC,gBAA0B,GAAG,EAAE;EACrC,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;EAEtCH,SAAS,CAACI,QAAQ,CAAC;IACjBC,WAAWA,CAACC,IAAI,EAAE;MAChBJ,YAAY,CAACK,GAAG,CAACD,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,OAAO,MAAqB;IAC1B,IAAIC,SAAS;IACb,GAAG;MACDjB,WAAW,CAACQ,gBAAgB,CAAC;MAC7BS,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAGX,gBAAgB,CAAC;IACtD,CAAC,QAAQC,YAAY,CAACW,GAAG,CAACH,SAAS,CAAC;IAEpC,OAAOI,WAAC,CAACC,WAAW,CAACD,WAAC,CAACE,UAAU,CAACN,SAAS,CAAC,CAAC;EAC/C,CAAC;AACH;AAQA,SAASO,qCAAqCA,CAC5CjB,SAA2D,EACtC;EACrB,IAAIkB,SAA8B;EAElC,OAAO,MAAqB;IAC1B,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAGnB,iCAAiC,CAACC,SAAS,CAAC;IAC1D;IAEA,OAAOkB,SAAS,CAAC,CAAC;EACpB,CAAC;AACH;AAUA,SAASC,mBAAmBA,CAC1Bb,IAAsD,EACtDc,SAA8D,EAI9D;EACA,MAAM1B,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACd,EAAE;EACvB,MAAM2B,KAAK,GAAGf,IAAI,CAACe,KAAK;EACxB,IAAIf,IAAI,CAACgB,IAAI,KAAK,kBAAkB,EAAE;IACpC,MAAMF,SAAS,GAAG1B,EAAE,CAACe,IAAI;IACzB,MAAMc,KAAK,GAAGF,KAAK,CAACG,gCAAgC,CAAC9B,EAAE,CAAC;IACxD,MAAM+B,OAAO,GAAGX,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC;IAEvCC,KAAK,CAACK,MAAM,CAACN,SAAS,EAAEG,KAAK,CAACd,IAAI,CAAC;IAEnCH,IAAI,CAACqB,GAAG,CAAC,IAAI,CAAC,CAACC,WAAW,CAACH,OAAO,CAAC;IAEnC,OAAO;MAAE/B,EAAE,EAAEoB,WAAC,CAACe,SAAS,CAACN,KAAK,CAAC;MAAEjB;IAAK,CAAC;EACzC,CAAC,MAAM;IACL,IAAIiB,KAAmB;IAEvB,IAAI7B,EAAE,EAAE;MACN0B,SAAS,GAAG1B,EAAE,CAACe,IAAI;MACnBc,KAAK,GAAGO,wBAAwB,CAACT,KAAK,CAACU,MAAM,EAAEX,SAAS,CAAC;MACzDC,KAAK,CAACK,MAAM,CAACN,SAAS,EAAEG,KAAK,CAACd,IAAI,CAAC;IACrC,CAAC,MAAM;MACLc,KAAK,GAAGO,wBAAwB,CAC9BT,KAAK,CAACU,MAAM,EACZ,OAAOX,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,iBAC9C,CAAC;IACH;IAEA,MAAMY,YAAY,GAAGlB,WAAC,CAACmB,eAAe,CACpC,OAAOb,SAAS,KAAK,QAAQ,GAAGN,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC,GAAG,IAAI,EAC9Dd,IAAI,CAACE,IAAI,CAAC0B,UAAU,EACpB5B,IAAI,CAACE,IAAI,CAAC2B,IACZ,CAAC;IAED,MAAM,CAACC,OAAO,CAAC,GAAG9B,IAAI,CAACsB,WAAW,CAChCd,WAAC,CAACuB,kBAAkB,CAAC,CAACL,YAAY,EAAET,KAAK,CAAC,CAC5C,CAAC;IAED,OAAO;MACL7B,EAAE,EAAEoB,WAAC,CAACe,SAAS,CAACN,KAAK,CAAC;MACtBjB,IAAI,EAAE8B,OAAO,CAACT,GAAG,CAAC,eAAe;IACnC,CAAC;EACH;AACF;AAEA,SAASW,qBAAqBA,CAC5BC,GAAiC,EACjCC,KAA+B,EAC/BC,QAAiB,EACyB;EAC1C,IAAIF,GAAG,CAACjB,IAAI,KAAK,aAAa,EAAE;IAC9B,OAAOR,WAAC,CAAC4B,oBAAoB,CAACH,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEF,QAAQ,CAAC;EAChE,CAAC,MAAM;IACL,OAAO3B,WAAC,CAAC8B,aAAa,CAACL,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEA,SAAS,EAAEF,QAAQ,CAAC;EACpE;AACF;AAEA,SAASI,yBAAyBA,CAChCvC,IAAuB,EACvBc,SAA8D,EAC9D;EACA,IAAI,CAACd,IAAI,CAACE,IAAI,CAACd,EAAE,EAAE;IACjBY,IAAI,CAACE,IAAI,CAACd,EAAE,GACV,OAAO0B,SAAS,KAAK,QAAQ,GACzBN,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC,GACvBd,IAAI,CAACe,KAAK,CAACyB,qBAAqB,CAAC,OAAO,CAAC;EACjD;AACF;AAEA,SAASC,oBAAoBA,CAC3B3B,SAAuB,EACvB4B,OAA0C,EAC1CC,SAAuC,EACvCC,SAAuC,EACvCC,SAAwB,EACxBC,UAAmB,EACnBX,QAAiB,EACjBY,OAA6B,EACvB;EACN,MAAMC,OAAO,GACX,CAACD,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDZ,QAAQ,GACJrB,SAAS,GACTN,WAAC,CAACyC,cAAc,CAAC,CAAC;EAExB,MAAMC,UAAU,GAAG1C,WAAC,CAAC2C,cAAc,CAAC,CAClC3C,WAAC,CAAC4C,eAAe,CACf5C,WAAC,CAAC6C,gBAAgB,CAAC7C,WAAC,CAACe,SAAS,CAACyB,OAAO,CAAC,EAAExC,WAAC,CAACe,SAAS,CAACsB,SAAS,CAAC,CACjE,CAAC,CACF,CAAC;EAEF,MAAMS,UAAU,GAAG9C,WAAC,CAAC2C,cAAc,CAAC,CAClC3C,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACgD,oBAAoB,CACpB,GAAG,EACHhD,WAAC,CAAC6C,gBAAgB,CAAC7C,WAAC,CAACe,SAAS,CAACyB,OAAO,CAAC,EAAExC,WAAC,CAACe,SAAS,CAACsB,SAAS,CAAC,CAAC,EAChErC,WAAC,CAACE,UAAU,CAAC,GAAG,CAClB,CACF,CAAC,CACF,CAAC;EAEF,IAAI+C,MAA4C,EAC9CC,MAA4C;EAE9C,IAAIf,SAAS,CAAC3B,IAAI,KAAK,aAAa,EAAE;IACpCyC,MAAM,GAAGjD,WAAC,CAACmD,kBAAkB,CAAC,KAAK,EAAEhB,SAAS,EAAE,EAAE,EAAEO,UAAU,EAAEf,QAAQ,CAAC;IACzEuB,MAAM,GAAGlD,WAAC,CAACmD,kBAAkB,CAC3B,KAAK,EACLf,SAAS,EACT,CAACpC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB4C,UAAU,EACVnB,QACF,CAAC;EACH,CAAC,MAAM;IACLsB,MAAM,GAAGjD,WAAC,CAACoD,WAAW,CACpB,KAAK,EACLjB,SAAS,EACT,EAAE,EACFO,UAAU,EACVJ,UAAU,EACVX,QACF,CAAC;IACDuB,MAAM,GAAGlD,WAAC,CAACoD,WAAW,CACpB,KAAK,EACLhB,SAAS,EACT,CAACpC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB4C,UAAU,EACVR,UAAU,EACVX,QACF,CAAC;EACH;EAEAO,OAAO,CAACmB,WAAW,CAACH,MAAM,CAAC;EAC3BhB,OAAO,CAACmB,WAAW,CAACJ,MAAM,CAAC;AAC7B;AAEA,SAASK,wBAAwBA,CAC/BjB,SAAwB,EACxBE,OAA6B,EACyB;EACtD,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,EAAE;IAC3E,OAAO,CACLgB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC7B;AACA,wBAAwBzD,WAAC,CAACe,SAAS,CAACsB,SAAS,CAAC;AAC9C;AACA,OAAO,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC7B;AACA,iBAAiBzD,WAAC,CAACe,SAAS,CAACsB,SAAS,CAAC;AACvC;AACA,OAAO,CACF;EACH;EACA,OAAO,CACLkB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC3B,eAAezD,WAAC,CAACe,SAAS,CAACsB,SAAS,CAAC;AACrC,KAAK,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC3B,oBAAoBzD,WAAC,CAACe,SAAS,CAACsB,SAAS,CAAC;AAC1C,KAAK,CACF;AACH;AAWA,SAASqB,yBAAyBA,CAChClE,IAA4B,EACJ;EACxBA,IAAI,GAAG,IAAAmE,oEAA2B,EAACnE,IAAI,CAAC;EACxC,IAAIA,IAAI,CAACoE,oBAAoB,CAAC,CAAC,EAAE;IAC/B,MAAMC,WAAW,GAAGrE,IAAI,CAACqB,GAAG,CAAC,aAAa,CAAC;IAC3C,OAAO6C,yBAAyB,CAACG,WAAW,CAACA,WAAW,CAAC/E,MAAM,GAAG,CAAC,CAAC,CAAC;EACvE;EACA,OAAOU,IAAI;AACb;AAYA,SAASsE,sBAAsBA,CAACtE,IAA4B,EAAgB;EAC1E,MAAM0C,OAAO,GAAGwB,yBAAyB,CAAClE,IAAI,CAAC;EAC/C,IAAI0C,OAAO,CAAC6B,oBAAoB,CAAC,CAAC,EAAE;IAClC,OAAO/D,WAAC,CAACe,SAAS,CAACvB,IAAI,CAACE,IAAI,CAAC;EAC/B,CAAC,MAAM,IAAIwC,OAAO,CAAC8B,YAAY,CAAC,CAAC,IAAIxE,IAAI,CAACe,KAAK,CAAC0D,MAAM,CAAC/B,OAAO,CAACxC,IAAI,CAACC,IAAI,CAAC,EAAE;IACzE,OAAOK,WAAC,CAACe,SAAS,CAACvB,IAAI,CAACE,IAAI,CAAC;EAC/B,CAAC,MAAM,IACLwC,OAAO,CAACgC,sBAAsB,CAAC,CAAC,IAChChC,OAAO,CAACrB,GAAG,CAAC,MAAM,CAAC,CAACmD,YAAY,CAAC,CAAC,EAClC;IACA,OAAOhE,WAAC,CAACe,SAAS,CAACmB,OAAO,CAACxC,IAAI,CAACyE,IAAoB,CAAC;EACvD,CAAC,MAAM;IACL,MAAM,IAAIC,KAAK,CACb,oCAAoC5E,IAAI,CAAC6E,QAAQ,CAAC,CAAC,6BACrD,CAAC;EACH;AACF;AAaA,SAASC,+BAA+BA,CACtCT,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAM9C,GAAG,GAAG8C,SAAS,CAAC1D,GAAG,CAAC,KAAK,CAA2B;EAC1D,IAAIY,GAAG,CAACmC,oBAAoB,CAAC,CAAC,EAAE;IAC9BC,WAAW,CAACW,IAAI,CAAC,GAAG/C,GAAG,CAAC/B,IAAI,CAACmE,WAAW,CAAC;EAC3C,CAAC,MAAM;IACLA,WAAW,CAACW,IAAI,CAAC/C,GAAG,CAAC/B,IAAI,CAAC;EAC5B;EACA+B,GAAG,CAACX,WAAW,CAAC2D,uBAAuB,CAACZ,WAAW,CAAC,CAAC;AACvD;AAcA,SAASa,8BAA8BA,CACrCb,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAM9C,GAAG,GAAG8C,SAAS,CAAC1D,GAAG,CAAC,KAAK,CAA2B;EAC1D,MAAM8D,UAAU,GAAGjB,yBAAyB,CAACjC,GAAG,CAAC;EACjD,IAAIkD,UAAU,CAACZ,oBAAoB,CAAC,CAAC,EAAE;IACrCO,+BAA+B,CAACT,WAAW,EAAEU,SAAS,CAAC;EACzD,CAAC,MAAM;IACL,MAAMK,WAAW,GAAGnD,GAAG,CAAClB,KAAK,CAACU,MAAM;IACpC,MAAM4D,eAAe,GAAG,IAAAC,wBAAkB,EACxCH,UAAU,CAACjF,IAAI,EACfkF,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;IACD,IAAI,CAACF,eAAe,EAAE;MAGpBP,+BAA+B,CAACT,WAAW,EAAEU,SAAS,CAAC;IACzD,CAAC,MAAM;MACL,MAAMS,kBAAkB,GAAG,CACzB,GAAGnB,WAAW,EAEd7D,WAAC,CAACe,SAAS,CAAC8D,eAAe,CAACV,IAAI,CAAC,CAClC;MACD,MAAMc,gBAAgB,GAAGN,UAAU,CAACO,UAAU;MAC9C,IAAID,gBAAgB,CAACrB,oBAAoB,CAAC,CAAC,EAAE;QAC3CqB,gBAAgB,CAACE,aAAa,CAAC,aAAa,EAAEH,kBAAkB,CAAC;MACnE,CAAC,MAAM;QACLL,UAAU,CAAC7D,WAAW,CACpB2D,uBAAuB,CAAC,CACtBzE,WAAC,CAACe,SAAS,CAAC8D,eAAe,CAAC,EAC5B,GAAGG,kBAAkB,CACtB,CACH,CAAC;MACH;IACF;EACF;AACF;AAWA,SAASI,oCAAoCA,CAC3CvB,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAMc,WAAW,GAAGd,SAAS,CAAC1D,GAAG,CAAC,OAAO,CAAC;EAC1C,IAAIwE,WAAW,CAAC3F,IAAI,EAAE;IACpBmE,WAAW,CAACW,IAAI,CAACa,WAAW,CAAC3F,IAAI,CAAC;EACpC,CAAC,MAAM,IAAImE,WAAW,CAAC/E,MAAM,GAAG,CAAC,EAAE;IACjC+E,WAAW,CAACA,WAAW,CAAC/E,MAAM,GAAG,CAAC,CAAC,GAAGkB,WAAC,CAACsF,eAAe,CACrD,MAAM,EACNzB,WAAW,CAACA,WAAW,CAAC/E,MAAM,GAAG,CAAC,CACpC,CAAC;EACH;EACAuG,WAAW,CAACvE,WAAW,CAAC2D,uBAAuB,CAACZ,WAAW,CAAC,CAAC;AAC/D;AAEA,SAAS0B,+BAA+BA,CACtC1B,WAA2B,EAC3B2B,SAAkC,EAClC;EACAA,SAAS,CAACC,gBAAgB,CACxB,MAAM,EACNzF,WAAC,CAAC+C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAAS6B,+BAA+BA,CACtC7B,WAA2B,EAC3B8B,eAAwC,EACxC;EACAA,eAAe,CAACjG,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACpCiB,WAAC,CAAC+C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAAS+B,yBAAyBA,CAChCpC,UAAwB,EACxBqC,aAA2B,EAC3B;EACA,OACE7F,WAAC,CAAC8F,gBAAgB,CAACtC,UAAU,CAAC,IAC9BxD,WAAC,CAACgE,YAAY,CAACR,UAAU,CAACuC,MAAM,EAAE;IAAEpG,IAAI,EAAEkG,aAAa,CAAClG;EAAK,CAAC,CAAC;AAEnE;AASA,SAASqG,+BAA+BA,CACtCnC,WAA2B,EAC3BoC,cAA4B,EAC5B;EACA,IAAIA,cAAc,EAAE;IAClB,IACEpC,WAAW,CAAC/E,MAAM,IAAI,CAAC,IACvB8G,yBAAyB,CAAC/B,WAAW,CAAC,CAAC,CAAC,EAAEoC,cAAc,CAAC,EACzD;MAEA,MAAMC,eAAe,GAAGlG,WAAC,CAACmG,cAAc,CAACnG,WAAC,CAACe,SAAS,CAACkF,cAAc,CAAC,EAAE,CACpEpC,WAAW,CAAC,CAAC,CAAC,CACf,CAAC;MACFA,WAAW,CAACuC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEF,eAAe,CAAC;IAC3C;IAEA,IACErC,WAAW,CAAC/E,MAAM,IAAI,CAAC,IACvBkB,WAAC,CAACqG,gBAAgB,CAACxC,WAAW,CAACA,WAAW,CAAC/E,MAAM,GAAG,CAAC,CAAC,CAAC,IACvD8G,yBAAyB,CACvB/B,WAAW,CAACA,WAAW,CAAC/E,MAAM,GAAG,CAAC,CAAC,EACnCmH,cACF,CAAC,EACD;MACApC,WAAW,CAACuC,MAAM,CAACvC,WAAW,CAAC/E,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/C;EACF;EACA,OAAO2F,uBAAuB,CAACZ,WAAW,CAAC;AAC7C;AAWA,SAASyC,0CAA0CA,CACjDzC,WAA2B,EAC3B8B,eAAwC,EACxCM,cAA4B,EAC5B;EACAN,eAAe,CAACrG,QAAQ,CAAC;IACvBiH,cAAc,EAAE;MACdC,IAAIA,CAAChH,IAAI,EAAE;QACT,IAAI,CAACA,IAAI,CAACqB,GAAG,CAAC,QAAQ,CAAC,CAAC4F,OAAO,CAAC,CAAC,EAAE;QACnC,MAAMC,QAAQ,GAAG,CACflH,IAAI,CAACE,IAAI,EACT,GAAGmE,WAAW,CAAC8C,GAAG,CAACC,IAAI,IAAI5G,WAAC,CAACe,SAAS,CAAC6F,IAAI,CAAC,CAAC,CAC9C;QAED,IAAIpH,IAAI,CAACqH,kBAAkB,CAAC,CAAC,EAAE;UAC7BH,QAAQ,CAAClC,IAAI,CAACxE,WAAC,CAACyC,cAAc,CAAC,CAAC,CAAC;QACnC;QACAjD,IAAI,CAACsB,WAAW,CACdkF,+BAA+B,CAACU,QAAQ,EAAET,cAAc,CAC1D,CAAC;QAEDzG,IAAI,CAACsH,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IACDC,WAAWA,CAACvH,IAAI,EAAE;MAChB,IAAIA,IAAI,CAACE,IAAI,CAACsH,IAAI,KAAK,aAAa,EAAE;QACpCxH,IAAI,CAACsH,IAAI,CAAC,CAAC;MACb;IACF;EACF,CAAC,CAAC;AACJ;AAWA,SAASG,gCAAgCA,CACvCpD,WAA2B,EAC3BqD,cAAuB,EACvB;EACA,MAAM7F,IAAmB,GAAG,CAC1BrB,WAAC,CAAC+C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAAC,CAC5D;EACD,IAAIqD,cAAc,EAAE;IAClB7F,IAAI,CAACtC,OAAO,CACViB,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACmG,cAAc,CAACnG,WAAC,CAACmH,KAAK,CAAC,CAAC,EAAE,CAACnH,WAAC,CAACoH,aAAa,CAACpH,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CACrE,CACF,CAAC;EACH;EACA,OAAOF,WAAC,CAACoD,WAAW,CAClB,aAAa,EACbpD,WAAC,CAACE,UAAU,CAAC,aAAa,CAAC,EAC3BgH,cAAc,GAAG,CAAClH,WAAC,CAACqH,WAAW,CAACrH,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAC3DF,WAAC,CAAC2C,cAAc,CAACtB,IAAI,CACvB,CAAC;AACH;AAEA,SAASiG,gCAAgCA,CAACzD,WAA2B,EAAE;EACrE,OAAO7D,WAAC,CAACuH,WAAW,CAAC,CACnBvH,WAAC,CAAC+C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAAC,CAC5D,CAAC;AACJ;AAGA,MAAM2D,KAAK,GAAG,CAAC;AACf,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAEhB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,oBAAoB,GAAG,EAAE;AAE/B,SAASC,cAAcA,CAAC9F,OAA0C,EAAU;EAC1E,QAAQA,OAAO,CAACxC,IAAI,CAACc,IAAI;IACvB,KAAK,eAAe;IACpB,KAAK,sBAAsB;MACzB,OAAOgH,KAAK;IACd,KAAK,uBAAuB;MAC1B,OAAOC,QAAQ;IACjB,KAAK,aAAa;IAClB,KAAK,oBAAoB;MACvB,IAAIvF,OAAO,CAACxC,IAAI,CAACsH,IAAI,KAAK,KAAK,EAAE;QAC/B,OAAOW,MAAM;MACf,CAAC,MAAM,IAAIzF,OAAO,CAACxC,IAAI,CAACsH,IAAI,KAAK,KAAK,EAAE;QACtC,OAAOY,MAAM;MACf,CAAC,MAAM;QACL,OAAOF,MAAM;MACf;EACJ;AACF;AAmCA,SAASO,qBAAqBA,CAACC,IAAqB,EAAmB;EACrE,OAAO,CACL,GAAGA,IAAI,CAACC,MAAM,CACZC,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MACzD,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CACZC,EAAE,IAAI,CAACA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MAC1D,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,EACtD,GAAGU,IAAI,CAACC,MAAM,CAACC,EAAE,IAAI,CAACA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,CACxD;AACH;AAgBA,SAASa,sBAAsBA,CAC7BC,UAAyB,EACzBC,cAA4C,EAC5ChG,OAA6B,EACC;EAC9B,MAAMiG,SAAS,GAAGF,UAAU,CAACxJ,MAAM;EACnC,MAAM2J,WAAW,GAAGF,cAAc,CAACG,IAAI,CAACC,OAAO,CAAC;EAChD,MAAMC,IAAoB,GAAG,EAAE;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;IAClC,KACGtG,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDkG,WAAW,EACX;MACAG,IAAI,CAACpE,IAAI,CACP+D,cAAc,CAACM,CAAC,CAAC,IAAI7I,WAAC,CAACsF,eAAe,CAAC,MAAM,EAAEtF,WAAC,CAAC8I,cAAc,CAAC,CAAC,CAAC,CACpE,CAAC;IACH;IACAF,IAAI,CAACpE,IAAI,CAAC8D,UAAU,CAACO,CAAC,CAAC,CAACrF,UAAU,CAAC;EACrC;EAEA,OAAO;IAAEuF,QAAQ,EAAEN,WAAW;IAAEG;EAAK,CAAC;AACxC;AAEA,SAASI,uBAAuBA,CAC9BC,cAA+B,EAC/B1G,OAA6B,EACV;EACnB,OAAOvC,WAAC,CAACkJ,eAAe,CACtBD,cAAc,CAACtC,GAAG,CAACyB,EAAE,IAAI;IACvB,IAAIe,IAAI,GAAGf,EAAE,CAACpB,IAAI;IAClB,IAAIoB,EAAE,CAACzG,QAAQ,EAAE;MACfwH,IAAI,IACF5G,OAAO,KAAK,SAAS,IACaA,OAAO,KAAK,SAAS,GACnDuF,MAAM,GACND,kBAAkB;IAC1B;IACA,IAAIO,EAAE,CAACgB,kBAAkB,EAAED,IAAI,IAAIpB,oBAAoB;IAEvD,OAAO/H,WAAC,CAACkJ,eAAe,CAAC,CACvBd,EAAE,CAACiB,eAAe,EAClBrJ,WAAC,CAAC8I,cAAc,CAACK,IAAI,CAAC,EACtBf,EAAE,CAACzI,IAAI,EACP,IAAIyI,EAAE,CAACkB,cAAc,IAAI,EAAE,CAAC,CAC7B,CAAC;EACJ,CAAC,CACH,CAAC;AACH;AAEA,SAASC,8BAA8BA,CAACN,cAA+B,EAAE;EACvE,MAAMO,QAAwB,GAAG,EAAE;EAEnC,KAAK,MAAMpB,EAAE,IAAIa,cAAc,EAAE;IAC/B,MAAM;MAAEQ;IAAO,CAAC,GAAGrB,EAAE;IAErB,IAAIsB,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzBD,QAAQ,CAAChF,IAAI,CAAC,GAAGiF,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAIA,MAAM,KAAK5H,SAAS,EAAE;MAC/B2H,QAAQ,CAAChF,IAAI,CAACiF,MAAM,CAAC;IACvB;EACF;EAEA,OAAOD,QAAQ;AACjB;AAEA,SAASI,mBAAmBA,CAC1BrH,OAA6B,EAC7BL,OAAiB,EACjBT,GAAkB,EAClBoI,KAAmB,EACnBC,KAAmB,EACnBnI,QAAiB,EACjB;EACAO,OAAO,CAACmB,WAAW,CACjBrD,WAAC,CAACmD,kBAAkB,CAClB,KAAK,EACLnD,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChB,EAAE,EACFzB,WAAC,CAAC2C,cAAc,CAAC,CACf3C,WAAC,CAAC4C,eAAe,CACf5C,WAAC,CAACmG,cAAc,CACdnG,WAAC,CAACe,SAAS,CAAC8I,KAAK,CAAC,EACetH,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACyC,cAAc,CAAC,CAAC,CACzB,CACF,CAAC,CACF,CAAC,EACFd,QACF,CACF,CAAC;EAEDO,OAAO,CAACmB,WAAW,CACjBrD,WAAC,CAACmD,kBAAkB,CAClB,KAAK,EACLnD,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChB,CAACzB,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAAC2C,cAAc,CAAC,CACf3C,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACmG,cAAc,CACdnG,WAAC,CAACe,SAAS,CAAC+I,KAAK,CAAC,EACevH,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,CAAC3B,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,GACnB,CAACF,WAAC,CAACyC,cAAc,CAAC,CAAC,EAAEzC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAC5C,CACF,CAAC,CACF,CAAC,EACFyB,QACF,CACF,CAAC;AACH;AAEA,SAASoI,mBAAmBA,CAC1B7H,OAAuC,EACvCT,GAAkB,EAClBuI,cAA4B,EAC5BrI,QAAiB,EACjB;EACA,IAAIsI,MAAwC;EAC5C,IAAIC,KAAoB;EAExB,IAAIhI,OAAO,CAACxC,IAAI,CAACsH,IAAI,KAAK,KAAK,EAAE;IAC/BiD,MAAM,GAAG,CAACjK,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5BgK,KAAK,GAAG,CACNlK,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACmG,cAAc,CAAC6D,cAAc,EAAE,CAC/BhK,WAAC,CAACyC,cAAc,CAAC,CAAC,EAClBzC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAClB,CACH,CAAC,CACF;EACH,CAAC,MAAM;IACL+J,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,CACNlK,WAAC,CAAC4C,eAAe,CAAC5C,WAAC,CAACmG,cAAc,CAAC6D,cAAc,EAAE,CAAChK,WAAC,CAACyC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1E;EACH;EAEAP,OAAO,CAACpB,WAAW,CACjBd,WAAC,CAACmD,kBAAkB,CAClBjB,OAAO,CAACxC,IAAI,CAACsH,IAAI,EACjBhH,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBwI,MAAM,EACNjK,WAAC,CAAC2C,cAAc,CAACuH,KAAK,CAAC,EACvBvI,QACF,CACF,CAAC;AACH;AAEA,SAASwI,6BAA6BA,CACpC3K,IAA4B,EACe;EAC3C,MAAM;IAAEgB;EAAK,CAAC,GAAGhB,IAAI;EAErB,OACEgB,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,kBAAkB,IAC3BA,IAAI,KAAK,aAAa;AAE1B;AAEA,SAAS4J,iBAAiBA,CAACF,KAAoB,EAAE;EAC/C,OAAOlK,WAAC,CAACmG,cAAc,CACrBnG,WAAC,CAACqK,uBAAuB,CAAC,EAAE,EAAErK,WAAC,CAAC2C,cAAc,CAACuH,KAAK,CAAC7I,IAAI,CAAC,CAAC,EAC3D,EACF,CAAC;AACH;AAEA,SAASiJ,4BAA4BA,CAACJ,KAAoB,EAAE;EAC1D,OAAOlK,WAAC,CAACuK,kBAAkB,CAAC,IAAI,EAAE,EAAE,EAAEvK,WAAC,CAAC2C,cAAc,CAACuH,KAAK,CAAC7I,IAAI,CAAC,CAAC;AACrE;AAEA,SAASmJ,yBAAyBA,CAAC9I,KAAmB,EAAE;EACtD,OAAO1B,WAAC,CAACuK,kBAAkB,CACzB,IAAI,EACJ,EAAE,EACFvK,WAAC,CAAC2C,cAAc,CAAC,CAAC3C,WAAC,CAAC4C,eAAe,CAAClB,KAAK,CAAC,CAAC,CAC7C,CAAC;AACH;AAEA,SAAS+C,uBAAuBA,CAACgG,KAAqB,EAAE;EACtD,IAAIA,KAAK,CAAC3L,MAAM,KAAK,CAAC,EAAE,OAAOkB,WAAC,CAACsF,eAAe,CAAC,MAAM,EAAEtF,WAAC,CAAC8I,cAAc,CAAC,CAAC,CAAC,CAAC;EAC7E,IAAI2B,KAAK,CAAC3L,MAAM,KAAK,CAAC,EAAE,OAAO2L,KAAK,CAAC,CAAC,CAAC;EACvC,OAAOzK,WAAC,CAACuB,kBAAkB,CAACkJ,KAAK,CAAC;AACpC;AASA,SAASC,yCAAyCA,CAAChL,IAA0B,EAAE;EAC7E,MAAM;IAAEuK,MAAM;IAAE5I,IAAI;IAAEjB,SAAS,EAAEuK,WAAW;IAAEC,KAAK,EAAEC;EAAQ,CAAC,GAAGnL,IAAI;EACrE,OAAOM,WAAC,CAACuK,kBAAkB,CACzB1I,SAAS,EAEToI,MAAM,EACN5I,IAAI,EACJsJ,WAAW,EACXE,OACF,CAAC;AACH;AAEA,SAASC,yBAAyBA,CAChCC,KAAiB,EACjBzK,SAAyC,EACzC;EACA,OAAON,WAAC,CAACmG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAC1DhL,WAAC,CAACyC,cAAc,CAAC,CAAC,EAClBnC,SAAS,CACV,CAAC;AACJ;AAEA,SAAS2K,uBAAuBA,CAACF,KAAiB,EAAEG,WAAyB,EAAE;EAC7E,OAAOlL,WAAC,CAACmG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CAACE,WAAW,CAAC,CAAC;AAC1E;AAEA,SAASC,8BAA8BA,CAACC,SAAwB,EAAE;EAChE,OAAOpL,WAAC,CAACqK,uBAAuB,CAC9B,CAACrK,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAACqL,gBAAgB,CAAC,IAAI,EAAErL,WAAC,CAACe,SAAS,CAACqK,SAAS,CAAC,EAAEpL,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CACpE,CAAC;AACH;AAEA,SAASoL,gBAAgBA,CAAC9H,UAAkB,EAAE;EAC5C,IAAI;IACFxD,WAAC,CAACuL,YAAY,CAAC/H,UAAU,EAAE9D,IAAI,IAAI;MACjC,IAAIM,WAAC,CAACwL,aAAa,CAAC9L,IAAI,CAAC,EAAE;QAGzB,MAAM,IAAI;MACZ;IACF,CAAC,CAAC;IACF,OAAO,KAAK;EACd,CAAC,CAAC,OAAA+L,OAAA,EAAM;IACN,OAAO,IAAI;EACb;AACF;AAUA,SAASC,oBAAoBA,CAAClM,IAA+C,EAAE;EAC7E,MAAM;IAAEE;EAAK,CAAC,GAAGF,IAAI;EACrBE,IAAI,CAACiM,QAAQ,GAAG,IAAI;EACpB,IAAI3L,WAAC,CAACgE,YAAY,CAACtE,IAAI,CAAC+B,GAAG,CAAC,EAAE;IAC5B/B,IAAI,CAAC+B,GAAG,GAAGzB,WAAC,CAAC4L,aAAa,CAAClM,IAAI,CAAC+B,GAAG,CAAC9B,IAAI,CAAC;EAC3C;AACF;AAEA,SAASkM,wBAAwBA,CAACrM,IAAc,EAAEJ,YAAsB,EAAE;EACxE,IAAI0M,6BAA6B,GAAG,KAAK;EACzC,IAAI1M,YAAY,CAACN,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAMiN,kBAAkB,GAAG,IAAAC,iCAAyB,EAGlD;MACAzM,WAAWA,CAACC,IAAI,EAAEuL,KAAK,EAAE;QACvB,IAAIA,KAAK,CAACkB,eAAe,CAAClM,GAAG,CAACP,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC,EAAE;UAChDmM,6BAA6B,GAAG,IAAI;UACpCtM,IAAI,CAAC0M,IAAI,CAAC,CAAC;QACb;MACF;IACF,CAAC,CAAC;IACF,MAAMD,eAAe,GAAG,IAAIE,GAAG,CAAe,CAAC;IAC/C,KAAK,MAAMxM,IAAI,IAAIP,YAAY,EAAE;MAC/B6M,eAAe,CAACG,GAAG,CAACzM,IAAI,EAAE,IAAI,CAAC;IACjC;IACAH,IAAI,CAACF,QAAQ,CAACyM,kBAAkB,EAAE;MAChCE,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;EACA,OAAOH,6BAA6B;AACtC;AAEA,SAASO,6BAA6BA,CACpC7M,IAAuB,EACvB8M,uBAAoC,EACpC;EACA,MAAMP,kBAAkB,GAAG,IAAAC,iCAAyB,EAGlD;IACAzM,WAAWA,CAACC,IAAI,EAAEuL,KAAK,EAAE;MACvB,IAAI,CAACA,KAAK,CAACkB,eAAe,CAAClM,GAAG,CAACP,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC,EAAE;MAEnD,MAAMuF,UAAU,GAAG1F,IAAI,CAAC0F,UAAU;MAClC,MAAMqH,gBAAgB,GAAGrH,UAAU,CAACA,UAAU;MAE9C,IAEGqH,gBAAgB,CAAC7M,IAAI,CAACc,IAAI,KAAK,sBAAsB,IACpD+L,gBAAgB,CAAC7M,IAAI,CAACyE,IAAI,KAAKe,UAAU,CAACxF,IAAI,IAEhD6M,gBAAgB,CAAC7M,IAAI,CAACc,IAAI,KAAK,kBAAkB,IAEjD+L,gBAAgB,CAAC7M,IAAI,CAACc,IAAI,KAAK,aAAa,IAE5C+L,gBAAgB,CAAC7M,IAAI,CAACc,IAAI,KAAK,cAAc,IAE5C+L,gBAAgB,CAAC7M,IAAI,CAACc,IAAI,KAAK,gBAAgB,IAC9C+L,gBAAgB,CAAC7M,IAAI,CAACgC,KAAK,KAAKwD,UAAU,CAACxF,IAAI,IAC/C6M,gBAAgB,CAACrH,UAAU,CAAC1E,IAAI,KAAK,eAAgB,IAEtD+L,gBAAgB,CAAC7M,IAAI,CAACc,IAAI,KAAK,gBAAgB,IAC9C+L,gBAAgB,CAAC7M,IAAI,CAACyE,IAAI,KAAKe,UAAU,CAACxF,IAAK,EACjD;QACA,MAAMF,IAAI,CAACgN,mBAAmB,CAC5B,kDAAkDhN,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,mCACrE,CAAC;MACH;IACF;EACF,CAAC,CAAC;EACF,MAAMsM,eAAe,GAAG,IAAIE,GAAG,CAAe,CAAC;EAC/C,KAAK,MAAMxM,IAAI,IAAI2M,uBAAuB,EAAE;IAC1CL,eAAe,CAACG,GAAG,CAACzM,IAAI,EAAE,IAAI,CAAC;EACjC;EACAH,IAAI,CAACF,QAAQ,CAACyM,kBAAkB,EAAE;IAChCE,eAAe,EAAEA;EACnB,CAAC,CAAC;AACJ;AAgBA,SAASQ,cAAcA,CACrBjN,IAAuB,EACvBuL,KAAiB,EACjB2B,aAAsB,EACtBC,oBAA6B,EAC7BrM,SAA8D,EAC9DsM,eAAoC,EACpCrK,OAA6B,EACP;EAAA,IAAAsK,aAAA,EAAAC,mBAAA;EACtB,MAAMzL,IAAI,GAAG7B,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC;EAElC,MAAMkM,eAAe,GAAGvN,IAAI,CAACE,IAAI,CAAC4I,UAAU;EAC5C,IAAI0E,oBAAoB,GAAG,KAAK;EAChC,IAAIC,0BAA0B,GAAG,KAAK;EACtC,IAAIC,oBAAoB,GAAG,KAAK;EAEhC,MAAMC,uBAAuB,GAAGhN,qCAAqC,CAACX,IAAI,CAAC;EAE3E,MAAM4N,gBAA0C,GAAG,EAAE;EACrD,MAAMxI,WAAkB,GAAGpF,IAAI,CAACe,KAAK,CAACU,MAAM;EAC5C,MAAMoM,iBAAiB,GAAGA,CACxB7J,UAAwB,EACxB8J,IAAY,EACZC,WAAqC,KAClC;IACH,MAAMC,gBAAgB,GAAGxM,wBAAwB,CAAC4D,WAAW,EAAE0I,IAAI,CAAC;IACpEC,WAAW,CAAC/I,IAAI,CAACxE,WAAC,CAACgD,oBAAoB,CAAC,GAAG,EAAEwK,gBAAgB,EAAEhK,UAAU,CAAC,CAAC;IAC3E,OAAOxD,WAAC,CAACe,SAAS,CAACyM,gBAAgB,CAAC;EACtC,CAAC;EAED,IAAIvH,cAA4B;EAChC,IAAIwH,eAA6B;EACjC,MAAMC,WAAW,IAAAb,aAAA,GAAGrN,IAAI,CAACE,IAAI,CAACd,EAAE,qBAAZiO,aAAA,CAAclN,IAAI;EAEtC,MAAMgO,YAAY,GAAG,OAAOrN,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGuB,SAAS;EAI1E,MAAM+L,+BAA+B,GAAIC,SAAsB,IAAK;IAClE,IAAI;MACF7N,WAAC,CAACuL,YAAY,CAACsC,SAAS,EAAEnO,IAAI,IAAI;QAChC,IACEM,WAAC,CAACqG,gBAAgB,CAAC3G,IAAI,CAAC,IACxBM,WAAC,CAACyG,OAAO,CAAC/G,IAAI,CAAC,IACfM,WAAC,CAAC8N,iBAAiB,CAACpO,IAAI,CAAC,IACzBM,WAAC,CAAC+N,iBAAiB,CAACrO,IAAI,CAAC,IACzBM,WAAC,CAACgE,YAAY,CAACtE,IAAI,EAAE;UAAEC,IAAI,EAAE;QAAY,CAAC,CAAC,IAC1C+N,WAAW,IAAI1N,WAAC,CAACgE,YAAY,CAACtE,IAAI,EAAE;UAAEC,IAAI,EAAE+N;QAAY,CAAC,CAAE,IAC3D1N,WAAC,CAACgO,cAAc,CAACtO,IAAI,CAAC,IAAIA,IAAI,CAACuO,IAAI,CAACtO,IAAI,KAAK,QAAS,EACvD;UAGA,MAAM,IAAI;QACZ;MACF,CAAC,CAAC;MACF,OAAO,KAAK;IACd,CAAC,CAAC,OAAAuO,QAAA,EAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAMC,oBAA8B,GAAG,EAAE;EAIzC,KAAK,MAAMjM,OAAO,IAAIb,IAAI,EAAE;IAC1B,IAAI,CAAC8I,6BAA6B,CAACjI,OAAO,CAAC,EAAE;MAC3C;IACF;IAEA,MAAMkM,WAAW,GAAGlM,OAAO,CAACxC,IAAI;IAEhC,IAAI,CAAC0O,WAAW,CAACC,MAAM,IAAIrO,WAAC,CAACwL,aAAa,CAAC4C,WAAW,CAAC3M,GAAG,CAAC,EAAE;MAC3D0M,oBAAoB,CAAC3J,IAAI,CAAC4J,WAAW,CAAC3M,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;IACpD;IAEA,IAAI2O,WAAW,CAACF,WAAW,CAAC,EAAE;MAC5B,QAAQA,WAAW,CAAC5N,IAAI;QACtB,KAAK,eAAe;UAElBoM,eAAe,CAAC2B,aAAa,CAC3BrM,OAAO,EACP6I,KACF,CAAC;UACD;QACF,KAAK,sBAAsB;UAEzB6B,eAAe,CAAC4B,oBAAoB,CAClCtM,OAAO,EACP6I,KACF,CAAC;UACD;QACF,KAAK,uBAAuB;UAE1B6B,eAAe,CAAC6B,qBAAqB,CACnCvM,OAAO,EACP6I,KACF,CAAC;UACD,IAAIxI,OAAO,KAAK,SAAS,EAAE;YACzB;UACF;QAEF;UACE,IAAI6L,WAAW,CAACC,MAAM,EAAE;YAAA,IAAAK,gBAAA;YACtB,CAAAA,gBAAA,GAAAjB,eAAe,YAAAiB,gBAAA,GAAfjB,eAAe,GAAKzM,wBAAwB,CAC1C4D,WAAW,EACX,YACF,CAAC;UACH,CAAC,MAAM;YAAA,IAAA+J,eAAA;YACL,CAAAA,eAAA,GAAA1I,cAAc,YAAA0I,eAAA,GAAd1I,cAAc,GAAKjF,wBAAwB,CACzC4D,WAAW,EACX,WACF,CAAC;UACH;UACA;MACJ;MACAoI,oBAAoB,GAAG,IAAI;MAC3BE,oBAAoB,KAApBA,oBAAoB,GAAKkB,WAAW,CAAC9F,UAAU,CAACI,IAAI,CAClDkF,+BACF,CAAC;IACH,CAAC,MAAM,IAAIQ,WAAW,CAAC5N,IAAI,KAAK,uBAAuB,EAAE;MAEvDoM,eAAe,CAAC6B,qBAAqB,CACnCvM,OAAO,EACP6I,KACF,CAAC;MACD,MAAM;QAAEtJ,GAAG;QAAEC,KAAK;QAAE2M,MAAM,EAAE1M,QAAQ;QAAEgK;MAAS,CAAC,GAAGyC,WAAW;MAE9D,MAAMQ,KAAK,GAAGzB,uBAAuB,CAAC,CAAC;MACvC,MAAM0B,QAAQ,GAAGrN,qBAAqB,CAACoN,KAAK,EAAElN,KAAK,EAAEC,QAAQ,CAAC;MAC9D,MAAMmN,OAAO,GAAG5M,OAAO,CAACrB,GAAG,CAAC,KAAK,CAAC;MAClC,MAAM,CAACS,OAAO,CAAC,GAAGY,OAAO,CAACpB,WAAW,CAAC+N,QAAQ,CAAC;MAE/C,IAAI1M,SAAS,EAAEC,SAAS;MACxB,IAAIuJ,QAAQ,IAAI,CAACmD,OAAO,CAAC/K,oBAAoB,CAAC,CAAC,EAAE;QAC/C5B,SAAS,GAAG,IAAA2C,wBAAkB,EAC5BmG,uBAAuB,CAACF,KAAK,EAAEtJ,GAAmB,CAAC,EACnDmD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAE;QACF3C,SAAS,GAAGpC,WAAC,CAACe,SAAS,CAACoB,SAAS,CAACgC,IAAoB,CAAC;MACzD,CAAC,MAAM;QACLhC,SAAS,GAAGnC,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC;QAC5BW,SAAS,GAAGpC,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC;MAC9B;MAEAM,yBAAyB,CAACvC,IAAI,EAAEc,SAAS,CAAC;MAE1C2B,oBAAoB,CAClBzC,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ0C,OAAO,EACPa,SAAS,EACTC,SAAS,EACTwM,KAAK,EACLjD,QAAQ,EACRhK,QAAQ,EACRY,OACF,CAAC;IACH;IAEA,IAAI,UAAU,IAAIL,OAAO,CAACxC,IAAI,IAAIwC,OAAO,CAACxC,IAAI,CAACiM,QAAQ,EAAE;MACvDsB,0BAA0B,KAA1BA,0BAA0B,GAAK,CAACrI,WAAW,CAACjD,QAAQ,CAACO,OAAO,CAACxC,IAAI,CAAC+B,GAAG,CAAC;IACxE;EACF;EAEA,IAAI,CAACsL,eAAe,IAAI,CAACC,oBAAoB,EAAE;IAC7C,IAAI,CAACxN,IAAI,CAACE,IAAI,CAACd,EAAE,IAAI,OAAO0B,SAAS,KAAK,QAAQ,EAAE;MAClDd,IAAI,CAACE,IAAI,CAACd,EAAE,GAAGoB,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC;IACxC;IACA,IAAIqN,YAAY,EAAE;MAChBnO,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACzBuI,gCAAgC,CAAC,CAC/BwD,yBAAyB,CAACC,KAAK,EAAE4C,YAAY,CAAC,CAC/C,CACH,CAAC;IACH;IAEA;EACF;EAEA,MAAMoB,oBAAqC,GAAG,EAAE;EAEhD,IAAIpJ,eAAoD;EACxD,MAAM2G,uBAAuB,GAAG,IAAIjN,GAAG,CAAS,CAAC;EAEjD,IAAI2P,cAA4B,EAAEC,YAA0B;EAC5D,IAAIC,mBAAwC,GAAG,IAAI;EAUnD,SAASC,gBAAgBA,CAAC7G,UAAyB,EAA0B;IAC3E,IAAI8G,cAAc,GAAG,KAAK;IAC1B,IAAIC,aAAa,GAAG,KAAK;IACzB,MAAM9G,cAAuC,GAAG,EAAE;IAClD,KAAK,MAAMsF,SAAS,IAAIvF,UAAU,EAAE;MAClC,MAAM;QAAE9E;MAAW,CAAC,GAAGqK,SAAS;MAChC,IAAIyB,MAAM;MACV,KACG/M,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDvC,WAAC,CAACuP,kBAAkB,CAAC/L,UAAU,CAAC,EAChC;QACA,IAAIxD,WAAC,CAACyG,OAAO,CAACjD,UAAU,CAAC8L,MAAM,CAAC,EAAE;UAChCA,MAAM,GAAGtP,WAAC,CAACyC,cAAc,CAAC,CAAC;QAC7B,CAAC,MAAM,IAAImC,WAAW,CAACjD,QAAQ,CAAC6B,UAAU,CAAC8L,MAAM,CAAC,EAAE;UAClDA,MAAM,GAAGtP,WAAC,CAACe,SAAS,CAACyC,UAAU,CAAC8L,MAAM,CAAC;QACzC,CAAC,MAAM;UAAA,IAAAE,oBAAA;UACL,CAAAA,oBAAA,GAAAN,mBAAmB,YAAAM,oBAAA,GAAnBN,mBAAmB,GAAKlO,wBAAwB,CAAC4D,WAAW,EAAE,KAAK,CAAC;UACpE0K,MAAM,GAAGtP,WAAC,CAACgD,oBAAoB,CAC7B,GAAG,EACHhD,WAAC,CAACe,SAAS,CAACmO,mBAAmB,CAAC,EAChC1L,UAAU,CAAC8L,MACb,CAAC;UACD9L,UAAU,CAAC8L,MAAM,GAAGtP,WAAC,CAACe,SAAS,CAACmO,mBAAmB,CAAC;QACtD;MACF;MACA3G,cAAc,CAAC/D,IAAI,CAAC8K,MAAM,CAAC;MAC3BF,cAAc,KAAdA,cAAc,GAAK,CAACxK,WAAW,CAACjD,QAAQ,CAAC6B,UAAU,CAAC;MACpD6L,aAAa,KAAbA,aAAa,GAAKzB,+BAA+B,CAACC,SAAS,CAAC;IAC9D;IACA,OAAO;MAAEuB,cAAc;MAAEC,aAAa;MAAE9G;IAAe,CAAC;EAC1D;EAEA,MAAMkH,uBAAuB,GAC3BxC,0BAA0B,IAGtBC,oBAAoB,IAAI3K,OAAO,KAAK,SAAU;EAEpD,IAAImN,8BAA8B,GAAG,KAAK;EAC1C,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,gBAAgC,GAAG,EAAE;EACzC,IAAIC,kBAAgC;EACpC,IAAIC,sBAAgD,GAAG,EAAE;EACzD,IAAI/C,eAAe,EAAE;IACnBiC,cAAc,GAAGhO,wBAAwB,CAAC4D,WAAW,EAAE,WAAW,CAAC;IACnE8K,8BAA8B,GAAGlQ,IAAI,CAACuQ,kBAAkB,CAAC,CAAC;IAC1D,CAAC;MAAEnR,EAAE,EAAEqQ,YAAY;MAAEzP;IAAK,CAAC,GAAGa,mBAAmB,CAACb,IAAI,EAAEc,SAAS,CAAC;IAElEd,IAAI,CAACE,IAAI,CAAC4I,UAAU,GAAG,IAAI;IAE3B,MAAM0H,uBAAuB,GAAGjD,eAAe,CAACrE,IAAI,CAAC4C,gBAAgB,CAAC;IACtE,MAAM;MAAE8D,cAAc;MAAEC,aAAa;MAAE9G;IAAe,CAAC,GACrD4G,gBAAgB,CAACpC,eAAe,CAAC;IAEnC,MAAM;MAAEhE,QAAQ;MAAEH;IAAK,CAAC,GAAGP,sBAAsB,CAC/C0E,eAAe,EACfxE,cAAc,EACdhG,OACF,CAAC;IACDoN,oBAAoB,GAAG5G,QAAQ,GAAG,CAAC,GAAG,CAAC;IACvC6G,gBAAgB,GAAGhH,IAAI;IAEvB,IACEyG,aAAa,IACZD,cAAc,IAAIK,uBAAwB,IAC3CO,uBAAuB,EACvB;MACAH,kBAAkB,GAAGxC,iBAAiB,CACpCrN,WAAC,CAACkJ,eAAe,CAAC0G,gBAAgB,CAAC,EACnC,WAAW,EACXxC,gBACF,CAAC;IACH;IAEA,IAAI,CAACJ,oBAAoB,EAAE;MAGzB,KAAK,MAAM9K,OAAO,IAAI1C,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC,EAAE;QAC3C,MAAM;UAAEnB;QAAK,CAAC,GAAGwC,OAAO;QACxB,MAAMI,UAAU,GAAG,UAAU,IAAI5C,IAAI,IAAIA,IAAI,CAACiM,QAAQ;QACtD,IAAIrJ,UAAU,EAAE;UACd,IAAIJ,OAAO,CAAC+N,eAAe,CAAC;YAAE5B,MAAM,EAAE;UAAK,CAAC,CAAC,EAAE;YAC7C,IAAI,CAACnM,OAAO,CAACrB,GAAG,CAAC,KAAK,CAAC,CAACkD,oBAAoB,CAAC,CAAC,EAAE;cAC9C,MAAMtC,GAAG,GAAI/B,IAAI,CAAqB+B,GAAG;cACzC,MAAMoD,eAAe,GAAG,IAAAC,wBAAkB,EACxCrD,GAAG,EACHmD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;cACD,IAAIF,eAAe,IAAI,IAAI,EAAE;gBAI3BnF,IAAI,CAAC+B,GAAG,GAAGzB,WAAC,CAACe,SAAS,CAAC8D,eAAe,CAACV,IAAI,CAAC;gBAC5C2L,sBAAsB,CAACtL,IAAI,CAACK,eAAe,CAAC;cAC9C;YACF;UACF,CAAC,MAAM,IAAIiL,sBAAsB,CAAChR,MAAM,GAAG,CAAC,EAAE;YAC5CwF,+BAA+B,CAC7BwL,sBAAsB,EACtB5N,OACF,CAAC;YACD4N,sBAAsB,GAAG,EAAE;UAC7B;QACF;MACF;IACF;EACF,CAAC,MAAM;IACL/N,yBAAyB,CAACvC,IAAI,EAAEc,SAAS,CAAC;IAC1C2O,YAAY,GAAGjP,WAAC,CAACe,SAAS,CAACvB,IAAI,CAACE,IAAI,CAACd,EAAE,CAAC;EAC1C;EAEA,IAAIsR,uBAAsC;EAC1C,IAAIC,8BAA8B,GAAG,KAAK;EAE1C,IAAIC,2BAA2B,GAAG,EAAE;EACpC,IAAIC,iCAAiD,GAAG,EAAE;EAE1D,IAAIrD,oBAAoB,EAAE;IACxB,IAAI/G,cAAc,EAAE;MAClB,MAAMJ,aAAa,GAAG7F,WAAC,CAACmG,cAAc,CAACnG,WAAC,CAACe,SAAS,CAACkF,cAAc,CAAC,EAAE,CAClEjG,WAAC,CAACyC,cAAc,CAAC,CAAC,CACnB,CAAC;MACF2N,2BAA2B,CAAC5L,IAAI,CAACqB,aAAa,CAAC;IACjD;IACA,KAAK,MAAM3D,OAAO,IAAIb,IAAI,EAAE;MAC1B,IAAI,CAAC8I,6BAA6B,CAACjI,OAAO,CAAC,EAAE;QAC3C,IACEmO,iCAAiC,CAACvR,MAAM,GAAG,CAAC,IAC5CoD,OAAO,CAACoO,aAAa,CAAC,CAAC,EACvB;UACA/K,+BAA+B,CAC7B8K,iCAAiC,EACjCnO,OACF,CAAC;UACDmO,iCAAiC,GAAG,EAAE;QACxC;QACA;MACF;MAEA,MAAM;QAAE3Q;MAAK,CAAC,GAAGwC,OAAO;MACxB,MAAMoG,UAAU,GAAG5I,IAAI,CAAC4I,UAAU;MAElC,MAAMiI,aAAa,GAAG,CAAC,EAACjI,UAAU,YAAVA,UAAU,CAAExJ,MAAM;MAE1C,MAAMwD,UAAU,GAAG,UAAU,IAAI5C,IAAI,IAAIA,IAAI,CAACiM,QAAQ;MAEtD,IAAIhM,IAAI,GAAG,aAAa;MAExB,IAAID,IAAI,CAAC+B,GAAG,CAACjB,IAAI,KAAK,aAAa,EAAE;QACnCb,IAAI,GAAGD,IAAI,CAAC+B,GAAG,CAAC7C,EAAE,CAACe,IAAI;MACzB,CAAC,MAAM,IAAI,CAAC2C,UAAU,IAAI5C,IAAI,CAAC+B,GAAG,CAACjB,IAAI,KAAK,YAAY,EAAE;QACxDb,IAAI,GAAGD,IAAI,CAAC+B,GAAG,CAAC9B,IAAI;MACtB;MACA,IAAI0J,eAAgE;MACpE,IAAID,kBAAkB;MAEtB,IAAImH,aAAa,EAAE;QACjB,MAAM;UAAEnB,cAAc;UAAEC,aAAa;UAAE9G;QAAe,CAAC,GACrD4G,gBAAgB,CAAC7G,UAAU,CAAC;QAC9B,MAAM;UAAEM,IAAI;UAAEG;QAAS,CAAC,GAAGV,sBAAsB,CAC/CC,UAAU,EACVC,cAAc,EACdhG,OACF,CAAC;QACD6G,kBAAkB,GAAGL,QAAQ;QAC7BM,eAAe,GAAGT,IAAI,CAAC9J,MAAM,KAAK,CAAC,GAAG8J,IAAI,CAAC,CAAC,CAAC,GAAG5I,WAAC,CAACkJ,eAAe,CAACN,IAAI,CAAC;QACvE,IAAIyG,aAAa,IAAKD,cAAc,IAAIK,uBAAwB,EAAE;UAChEpG,eAAe,GAAGgE,iBAAiB,CACjChE,eAAe,EACf1J,IAAI,GAAG,MAAM,EACbmQ,sBACF,CAAC;QACH;MACF;MAEA,IAAIxN,UAAU,EAAE;QACd,IAAI,CAACJ,OAAO,CAACrB,GAAG,CAAC,KAAK,CAAC,CAACkD,oBAAoB,CAAC,CAAC,EAAE;UAC9C,MAAMtC,GAAG,GAAG/B,IAAI,CAAC+B,GAAmB;UACpC,MAAMoD,eAAe,GAAG,IAAAC,wBAAkB,EACxCyL,aAAa,GAAGtF,uBAAuB,CAACF,KAAK,EAAEtJ,GAAG,CAAC,GAAGA,GAAG,EACzDmD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;UACD,IAAIF,eAAe,IAAI,IAAI,EAAE;YAI3B,IAAIkI,eAAe,IAAI7K,OAAO,CAAC+N,eAAe,CAAC;cAAE5B,MAAM,EAAE;YAAK,CAAC,CAAC,EAAE;cAChE3O,IAAI,CAAC+B,GAAG,GAAGzB,WAAC,CAACe,SAAS,CAAC8D,eAAe,CAACV,IAAI,CAAC;cAC5C2L,sBAAsB,CAACtL,IAAI,CAACK,eAAe,CAAC;YAC9C,CAAC,MAAM;cACLnF,IAAI,CAAC+B,GAAG,GAAGoD,eAAe;YAC5B;UACF;QACF;MACF;MAEA,MAAM;QAAEpD,GAAG;QAAE4M,MAAM,EAAE1M;MAAS,CAAC,GAAGjC,IAAI;MAEtC,MAAM8Q,SAAS,GAAG/O,GAAG,CAACjB,IAAI,KAAK,aAAa;MAE5C,MAAMwG,IAAI,GAAGgB,cAAc,CAAC9F,OAAO,CAAC;MAEpC,IAAIsO,SAAS,IAAI,CAAC7O,QAAQ,EAAE;QAC1B,IAAI4O,aAAa,EAAE;UACjBJ,8BAA8B,GAAG,IAAI;QACvC;QACA,IAAInQ,WAAC,CAACyQ,sBAAsB,CAAC/Q,IAAI,CAAC,IAAI,CAACwQ,uBAAuB,EAAE;UAC9DA,uBAAuB,GAAGzO,GAAG;QAC/B;MACF;MAEA,IAAIS,OAAO,CAACwO,aAAa,CAAC;QAAE1J,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;QAClDrB,eAAe,GAAGzD,OAAO;MAC3B;MAEA,IAAIuH,MAAsB;MAC1B,IAAI8G,aAAa,EAAE;QACjB,IAAIjH,cAEH;QAED,IAAIqH,QAAsB;QAE1B,IAAIrO,UAAU,EAAE;UACdqO,QAAQ,GAAG7M,sBAAsB,CAC/B5B,OAAO,CAACrB,GAAG,CAAC,KAAK,CACnB,CAAC;QACH,CAAC,MAAM,IAAIY,GAAG,CAACjB,IAAI,KAAK,aAAa,EAAE;UACrCmQ,QAAQ,GAAG3Q,WAAC,CAAC4L,aAAa,CAACnK,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;QACzC,CAAC,MAAM,IAAI8B,GAAG,CAACjB,IAAI,KAAK,YAAY,EAAE;UACpCmQ,QAAQ,GAAG3Q,WAAC,CAAC4L,aAAa,CAACnK,GAAG,CAAC9B,IAAI,CAAC;QACtC,CAAC,MAAM;UACLgR,QAAQ,GAAG3Q,WAAC,CAACe,SAAS,CAACU,GAAmB,CAAC;QAC7C;QAEA,IAAIuF,IAAI,KAAKS,QAAQ,EAAE;UACrB,MAAM;YAAE/F;UAAM,CAAC,GAAGQ,OAAO,CAACxC,IAA+B;UAEzD,MAAMuK,MAAsB,GACO1H,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACyC,cAAc,CAAC,CAAC,CAAC;UAE1B,IAAIf,KAAK,EAAE;YACTuI,MAAM,CAACzF,IAAI,CAACxE,WAAC,CAACe,SAAS,CAACW,KAAK,CAAC,CAAC;UACjC;UAEA,MAAMkN,KAAK,GAAGzB,uBAAuB,CAAC,CAAC;UACvC,MAAMyD,cAAc,GAAG5P,wBAAwB,CAC7C4D,WAAW,EACX,QAAQjF,IAAI,EACd,CAAC;UACD,MAAMkR,QAAQ,GAAG7Q,WAAC,CAACmG,cAAc,CAC/BnG,WAAC,CAACe,SAAS,CAAC6P,cAAc,CAAC,EAC3B3G,MACF,CAAC;UAED,MAAM4E,QAAQ,GAAGrN,qBAAqB,CAACoN,KAAK,EAAEiC,QAAQ,EAAElP,QAAQ,CAAC;UACjE,MAAM,CAACL,OAAO,CAAC,GAAGY,OAAO,CAACpB,WAAW,CAAC+N,QAAQ,CAAC;UAE/C,IAAI2B,SAAS,EAAE;YACblH,cAAc,GAAGhG,wBAAwB,CAACsL,KAAK,EAAErM,OAAO,CAAC;YAEzD,MAAMsH,KAAK,GAAG7I,wBAAwB,CAAC4D,WAAW,EAAE,OAAOjF,IAAI,EAAE,CAAC;YAClE,MAAMmK,KAAK,GAAG9I,wBAAwB,CAAC4D,WAAW,EAAE,OAAOjF,IAAI,EAAE,CAAC;YAElEiK,mBAAmB,CAACrH,OAAO,EAAEjB,OAAO,EAAEG,GAAG,EAAEoI,KAAK,EAAEC,KAAK,EAAEnI,QAAQ,CAAC;YAElE8H,MAAM,GAAG,CAACmH,cAAc,EAAE/G,KAAK,EAAEC,KAAK,CAAC;UACzC,CAAC,MAAM;YACL/H,yBAAyB,CAACvC,IAAI,EAAEc,SAAS,CAAC;YAC1C2B,oBAAoB,CAClBzC,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ0C,OAAO,EACPtB,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBzB,WAAC,CAACkE,sBAAsB,CAACzC,GAAG,CAAC,GACzBzB,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC0C,IAAoB,CAAC,GACrCnE,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EACpBmN,KAAK,EACLtM,UAAU,EACVX,QAAQ,EACRY,OACF,CAAC;YACDkH,MAAM,GAAG,CAACmH,cAAc,CAAC;UAC3B;QACF,CAAC,MAAM,IAAI5J,IAAI,KAAKQ,KAAK,EAAE;UACzB,MAAMsJ,MAAM,GAAG9P,wBAAwB,CAAC4D,WAAW,EAAE,QAAQjF,IAAI,EAAE,CAAC;UACpE,MAAMoR,SAAS,GACb7O,OAAO,CACPrB,GAAG,CAAC,OAAO,CAAC;UAEd,MAAMmQ,IAAoB,GACSzO,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACyC,cAAc,CAAC,CAAC,CAAC;UAC1B,IAAIsO,SAAS,CAACrR,IAAI,EAAEsR,IAAI,CAACxM,IAAI,CAACuM,SAAS,CAACrR,IAAI,CAAC;UAE7CqR,SAAS,CAACjQ,WAAW,CAACd,WAAC,CAACmG,cAAc,CAACnG,WAAC,CAACe,SAAS,CAAC+P,MAAM,CAAC,EAAEE,IAAI,CAAC,CAAC;UAElEvH,MAAM,GAAG,CAACqH,MAAM,CAAC;UAEjB,IAAIN,SAAS,EAAE;YACblH,cAAc,GAAGhG,wBAAwB,CAAC7B,GAAG,EAAEc,OAAO,CAAC;UACzD;QACF,CAAC,MAAM,IAAIiO,SAAS,EAAE;UACpB,MAAMS,MAAM,GAAGjQ,wBAAwB,CAAC4D,WAAW,EAAE,QAAQjF,IAAI,EAAE,CAAC;UACpE8J,MAAM,GAAG,CAACwH,MAAM,CAAC;UAEjB,MAAMC,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtCzE,aAAa;YACb0E,UAAU,EAAElP,OAAyC;YACrDmP,SAAS,EAAEpC,YAAY;YACvBqC,QAAQ,EAAE9R,IAAI,CAACE,IAAI,CAAC0B,UAAU;YAC9BmQ,IAAI,EAAExG,KAAK,CAACwG,IAAI;YAChBC,aAAa,EAAEvC;UACjB,CAAC,CAAC;UAEFiC,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvBnI,cAAc,GAAG,CACfoB,yCAAyC,CACvCxI,OAAO,CAACxC,IACV,CAAC,CACF;UAED,IAAIsH,IAAI,KAAKW,MAAM,IAAIX,IAAI,KAAKY,MAAM,EAAE;YACtCmC,mBAAmB,CACjB7H,OAAO,EACPlC,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBzB,WAAC,CAACe,SAAS,CAACkQ,MAAM,CAAC,EACnBtP,QACF,CAAC;UACH,CAAC,MAAM;YACL,MAAMjC,IAAI,GAAGwC,OAAO,CAACxC,IAA4B;YAGjDF,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACzBiB,WAAC,CAAC4B,oBAAoB,CAACH,GAAG,EAAEzB,WAAC,CAACe,SAAS,CAACkQ,MAAM,CAAC,EAAE,EAAE,EAAEvR,IAAI,CAAC2O,MAAM,CAClE,CAAC;YAED/B,uBAAuB,CAAC7M,GAAG,CAACgC,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;YAExCuC,OAAO,CAACwP,MAAM,CAAC,CAAC;UAClB;QACF;QAEA3C,oBAAoB,CAACvK,IAAI,CAAC;UACxBwC,IAAI;UACJqC,eAAe;UACfD,kBAAkB;UAClBzJ,IAAI,EAAEgR,QAAQ;UACdhP,QAAQ;UACR2H,cAAc;UACdG;QACF,CAAC,CAAC;QAEF,IAAIvH,OAAO,CAACxC,IAAI,EAAE;UAChBwC,OAAO,CAACxC,IAAI,CAAC4I,UAAU,GAAG,IAAI;QAChC;MACF;MAEA,IAAIhG,UAAU,IAAIwN,sBAAsB,CAAChR,MAAM,GAAG,CAAC,EAAE;QACnD,IAAIiO,eAAe,IAAI7K,OAAO,CAAC+N,eAAe,CAAC;UAAE5B,MAAM,EAAE;QAAK,CAAC,CAAC,EAAE,CAMlE,CAAC,MAAM;UACL/J,+BAA+B,CAC7BwL,sBAAsB,EACrB9I,IAAI,KAAKS,QAAQ,GACdvF,OAAO,CAACyP,cAAc,CAAC,CAAC,GACxBzP,OACN,CAAC;UACD4N,sBAAsB,GAAG,EAAE;QAC7B;MACF;MAEA,IACEM,2BAA2B,CAACtR,MAAM,GAAG,CAAC,IACtC,CAAC6C,QAAQ,KACRqF,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACArC,oCAAoC,CAClCgL,2BAA2B,EAC3BlO,OACF,CAAC;QACDkO,2BAA2B,GAAG,EAAE;MAClC;MAEA,IACEC,iCAAiC,CAACvR,MAAM,GAAG,CAAC,IAC5C6C,QAAQ,KACPqF,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACArC,oCAAoC,CAClCiL,iCAAiC,EACjCnO,OACF,CAAC;QACDmO,iCAAiC,GAAG,EAAE;MACxC;MAEA,IAAIE,aAAa,IAAIhO,OAAO,KAAK,SAAS,EAAE;QAC1C,IAAIyE,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,EAAE;UACvC,MAAMmK,WAAW,GAAG5Q,wBAAwB,CAC1C4D,WAAW,EACX,cAAcjF,IAAI,EACpB,CAAC;UACD8J,MAAM,CAACjF,IAAI,CAACoN,WAAW,CAAC;UACxB,MAAMC,aAAa,GAAG7R,WAAC,CAACmG,cAAc,CACpCnG,WAAC,CAACe,SAAS,CAAC6Q,WAAW,CAAC,EACxBjQ,QAAQ,GAAG,EAAE,GAAG,CAAC3B,WAAC,CAACyC,cAAc,CAAC,CAAC,CACrC,CAAC;UACD,IAAI,CAACd,QAAQ,EAAE;YACbyO,2BAA2B,CAAC5L,IAAI,CAACqN,aAAa,CAAC;UACjD,CAAC,MAAM;YACLxB,iCAAiC,CAAC7L,IAAI,CAACqN,aAAa,CAAC;UACvD;QACF;MACF;IACF;EACF;EAEA,IAAI/B,sBAAsB,CAAChR,MAAM,GAAG,CAAC,EAAE;IACrC,MAAMgT,QAAQ,GAAGtS,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC;IACtC,IAAIkR,mBAA8D;IAClE,KAAK,IAAIlJ,CAAC,GAAGiJ,QAAQ,CAAChT,MAAM,GAAG,CAAC,EAAE+J,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7C,MAAMrJ,IAAI,GAAGsS,QAAQ,CAACjJ,CAAC,CAAC;MACxB,MAAMnJ,IAAI,GAAGF,IAAI,CAACE,IAAuC;MACzD,IAAIA,IAAI,CAACiM,QAAQ,EAAE;QACjB,IAAIoB,eAAe,IAAI/M,WAAC,CAACiQ,eAAe,CAACvQ,IAAI,EAAE;UAAE2O,MAAM,EAAE;QAAK,CAAC,CAAC,EAAE;UAChE;QACF;QACA0D,mBAAmB,GAAGvS,IAAiD;QACvE;MACF;IACF;IACA,IAAIuS,mBAAmB,IAAI,IAAI,EAAE;MAC/BrN,8BAA8B,CAC5BoL,sBAAsB,EACtBiC,mBACF,CAAC;MACDjC,sBAAsB,GAAG,EAAE;IAC7B,CAAC,MAAM,CAIP;EACF;EAEA,IAAIM,2BAA2B,CAACtR,MAAM,GAAG,CAAC,EAAE;IAC1C,MAAMoI,cAAc,GAAG,CAAC,CAAC1H,IAAI,CAACE,IAAI,CAAC0B,UAAU;IAC7C,IAAIuE,eAAe,EAAE;MACnB,IAAIuB,cAAc,EAAE;QAClBZ,0CAA0C,CACxC8J,2BAA2B,EAC3BzK,eAAe,EACfM,cACF,CAAC;MACH,CAAC,MAAM;QACLP,+BAA+B,CAC7B0K,2BAA2B,EAC3BzK,eACF,CAAC;MACH;IACF,CAAC,MAAM;MACLnG,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACzBkI,gCAAgC,CAC9BmJ,2BAA2B,EAC3BlJ,cACF,CACF,CAAC;IACH;IACAkJ,2BAA2B,GAAG,EAAE;EAClC;EAEA,IAAIC,iCAAiC,CAACvR,MAAM,GAAG,CAAC,EAAE;IAChDU,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACmD,IAAI,CACtB8C,gCAAgC,CAAC+I,iCAAiC,CACpE,CAAC;IACDA,iCAAiC,GAAG,EAAE;EACxC;EAEA,MAAM2B,0BAA0B,GAC9B/J,qBAAqB,CAAC8G,oBAAoB,CAAC;EAE7C,MAAMkD,kBAAkB,GAAGjJ,uBAAuB,CAChBzG,OAAO,KAAK,SAAS,GACjDwM,oBAAoB,GACpBiD,0BAA0B,EAC9BzP,OACF,CAAC;EAED,MAAM2P,aAA6B,GAAG3I,8BAA8B,CAClEyI,0BACF,CAAC;EAED,IAAI/L,cAAc,EAAE;IAClBiM,aAAa,CAAC1N,IAAI,CAACyB,cAAc,CAAC;EACpC;EAEA,IAAIwH,eAAe,EAAE;IACnByE,aAAa,CAAC1N,IAAI,CAACiJ,eAAe,CAAC;EACrC;EAEA,MAAM0E,WAA2B,GAAG,EAAE;EACtC,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,MAAMC,aAAa,GACjBrD,cAAc,IAAIhP,WAAC,CAACmG,cAAc,CAACnG,WAAC,CAACe,SAAS,CAACiO,cAAc,CAAC,EAAE,EAAE,CAAC;EAErE,IAAIsD,iBAAiB,GAAG9S,IAAI;EAC5B,MAAM+S,aAAa,GAAG/S,IAAI,CAACE,IAAI;EAE/B,MAAM8S,cAAwC,GAAG,EAAE;EACnD,IAAIzF,eAAe,EAAE;IACnBoF,WAAW,CAAC3N,IAAI,CAACyK,YAAY,EAAED,cAAc,CAAC;IAC9C,MAAMyD,OAIH,GAAG,EAAE;IACRjT,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC,CAAC6R,OAAO,CAACxQ,OAAO,IAAI;MAGvC,IAAIA,OAAO,CAACoO,aAAa,CAAC,CAAC,EAAE;QAC3B,IAAIzE,wBAAwB,CAAC3J,OAAO,EAAEiM,oBAAoB,CAAC,EAAE;UAC3D,MAAMwE,oBAAoB,GAAGtF,iBAAiB,CAC5C/C,4BAA4B,CAACpI,OAAO,CAACxC,IAAI,CAAC,EAC1C,aAAa,EACb8S,cACF,CAAC;UACDnC,iCAAiC,CAAC7L,IAAI,CACpCxE,WAAC,CAACmG,cAAc,CACdnG,WAAC,CAAC6C,gBAAgB,CAAC8P,oBAAoB,EAAE3S,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,EAC9D,CAACF,WAAC,CAACyC,cAAc,CAAC,CAAC,CACrB,CACF,CAAC;QACH,CAAC,MAAM;UACL4N,iCAAiC,CAAC7L,IAAI,CACpC4F,iBAAiB,CAAClI,OAAO,CAACxC,IAAI,CAChC,CAAC;QACH;QACAwC,OAAO,CAACwP,MAAM,CAAC,CAAC;QAChB;MACF;MAEA,IACE,CAACxP,OAAO,CAAC+N,eAAe,CAAC,CAAC,IAAI/N,OAAO,CAACuO,sBAAsB,CAAC,CAAC,KAC9DvO,OAAO,CAACxC,IAAI,CAAC2O,MAAM,EACnB;QACA,MAAM0C,SAAS,GACb7O,OAAO,CACPrB,GAAG,CAAC,OAAO,CAAC;QACd,IAAIgL,wBAAwB,CAACkF,SAAS,EAAE5C,oBAAoB,CAAC,EAAE;UAC7D,MAAMyE,mBAAmB,GAAGvF,iBAAiB,CAC3C7C,yBAAyB,CAACuG,SAAS,CAACrR,IAAI,CAAC,EACzC,YAAY,EACZ8S,cACF,CAAC;UACDzB,SAAS,CAACjQ,WAAW,CACnBd,WAAC,CAACmG,cAAc,CACdnG,WAAC,CAAC6C,gBAAgB,CAAC+P,mBAAmB,EAAE5S,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,EAC7D,CAACF,WAAC,CAACyC,cAAc,CAAC,CAAC,CACrB,CACF,CAAC;QACH;QACA,IAAI4N,iCAAiC,CAACvR,MAAM,GAAG,CAAC,EAAE;UAChDsG,oCAAoC,CAClCiL,iCAAiC,EACjCnO,OACF,CAAC;UACDmO,iCAAiC,GAAG,EAAE;QACxC;QACAnO,OAAO,CAACxC,IAAI,CAAC2O,MAAM,GAAG,KAAK;QAC3BoE,OAAO,CAACjO,IAAI,CAACtC,OAAO,CAACxC,IAAI,CAAC;QAC1BwC,OAAO,CAACwP,MAAM,CAAC,CAAC;MAClB,CAAC,MAAM,IAAIxP,OAAO,CAAC2Q,oBAAoB,CAAC;QAAExE,MAAM,EAAE;MAAK,CAAC,CAAC,EAAE;QAGzD,IAAIxC,wBAAwB,CAAC3J,OAAO,EAAEiM,oBAAoB,CAAC,EAAE;UAC3D,MAAM+C,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtCzE,aAAa;YACb0E,UAAU,EAAElP,OAAO;YACnBmP,SAAS,EAAEpC,YAAY;YACvBqC,QAAQ,EAAE9R,IAAI,CAACE,IAAI,CAAC0B,UAAU;YAC9BmQ,IAAI,EAAExG,KAAK,CAACwG,IAAI;YAChBC,aAAa,EAAEvC;UACjB,CAAC,CAAC;UAEFiC,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvB,MAAMqB,uBAAuB,GAAGzF,iBAAiB,CAC/C3C,yCAAyC,CAACxI,OAAO,CAACxC,IAAI,CAAC,EACvDwC,OAAO,CAACrB,GAAG,CAAC,QAAQ,CAAC,CAACnB,IAAI,CAACC,IAAI,EAC/B6S,cACF,CAAC;UAED,IAAI7F,oBAAoB,EAAE;YACxBzK,OAAO,CAACxC,IAAI,CAACuK,MAAM,GAAG,CAACjK,WAAC,CAACqH,WAAW,CAACrH,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1DgC,OAAO,CAACxC,IAAI,CAAC2B,IAAI,GAAGrB,WAAC,CAAC2C,cAAc,CAAC,CACnC3C,WAAC,CAAC4C,eAAe,CACf5C,WAAC,CAACmG,cAAc,CACdnG,WAAC,CAAC6C,gBAAgB,CAChBiQ,uBAAuB,EACvB9S,WAAC,CAACE,UAAU,CAAC,OAAO,CACtB,CAAC,EACD,CAACF,WAAC,CAACyC,cAAc,CAAC,CAAC,EAAEzC,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAC1C,CACF,CAAC,CACF,CAAC;UACJ,CAAC,MAAM;YACLgC,OAAO,CAACxC,IAAI,CAACuK,MAAM,GAAG/H,OAAO,CAACxC,IAAI,CAACuK,MAAM,CAACtD,GAAG,CAAC,CAACoM,CAAC,EAAElK,CAAC,KAAK;cACtD,IAAI7I,WAAC,CAACgT,aAAa,CAACD,CAAC,CAAC,EAAE;gBACtB,OAAO/S,WAAC,CAACqH,WAAW,CAACrH,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAAC;cAC3C,CAAC,MAAM;gBACL,OAAOF,WAAC,CAACE,UAAU,CAAC,GAAG,GAAG2I,CAAC,CAAC;cAC9B;YACF,CAAC,CAAC;YACF3G,OAAO,CAACxC,IAAI,CAAC2B,IAAI,GAAGrB,WAAC,CAAC2C,cAAc,CAAC,CACnC3C,WAAC,CAAC4C,eAAe,CACf5C,WAAC,CAACmG,cAAc,CACdnG,WAAC,CAAC6C,gBAAgB,CAChBiQ,uBAAuB,EACvB9S,WAAC,CAACE,UAAU,CAAC,OAAO,CACtB,CAAC,EACD,CAACF,WAAC,CAACyC,cAAc,CAAC,CAAC,EAAEzC,WAAC,CAACE,UAAU,CAAC,WAAW,CAAC,CAChD,CACF,CAAC,CACF,CAAC;UACJ;QACF;QACAgC,OAAO,CAACxC,IAAI,CAAC2O,MAAM,GAAG,KAAK;QAC3BoE,OAAO,CAACjO,IAAI,CAACtC,OAAO,CAACxC,IAAI,CAAC;QAC1BwC,OAAO,CAACwP,MAAM,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,IAAIe,OAAO,CAAC3T,MAAM,GAAG,CAAC,IAAIuR,iCAAiC,CAACvR,MAAM,GAAG,CAAC,EAAE;MACtE,MAAMmU,YAAY,GAAG1P,cAAQ,CAACC,UAAU,CAACC,GAAG;AAClD,wBAAwBsH,KAAK,CAACC,SAAS,CAAC,UAAU,CAAC;AACnD,OAA4B;MACtBiI,YAAY,CAAC5R,IAAI,CAACA,IAAI,GAAG,CAOvBrB,WAAC,CAAC8B,aAAa,CACb9B,WAAC,CAACkT,YAAY,CAACX,aAAa,CAAC,EAC7B1Q,SAAS,EACTA,SAAS,EACTA,SAAS,EACM,IAAI,EACN,IACf,CAAC,EACD,GAAG4Q,OAAO,CACX;MAED,MAAMU,eAA+B,GAAG,EAAE;MAE1C,MAAMC,OAAO,GAAGpT,WAAC,CAACqT,aAAa,CAACJ,YAAY,EAAE,EAAE,CAAC;MAEjD,IAAI5C,iCAAiC,CAACvR,MAAM,GAAG,CAAC,EAAE;QAChDqU,eAAe,CAAC3O,IAAI,CAAC,GAAG6L,iCAAiC,CAAC;MAC5D;MACA,IAAIgC,aAAa,EAAE;QACjBD,iBAAiB,GAAG,IAAI;QACxBe,eAAe,CAAC3O,IAAI,CAAC6N,aAAa,CAAC;MACrC;MACA,IAAIc,eAAe,CAACrU,MAAM,GAAG,CAAC,EAAE;QAC9BqU,eAAe,CAACpU,OAAO,CACrBiB,WAAC,CAACmG,cAAc,CAACnG,WAAC,CAACmH,KAAK,CAAC,CAAC,EAAE,CAACnH,WAAC,CAACe,SAAS,CAACkO,YAAY,CAAC,CAAC,CACzD,CAAC;QAGDgE,YAAY,CAAC5R,IAAI,CAACA,IAAI,CAACmD,IAAI,CACzByC,gCAAgC,CAC9BkM,eAAe,EACM,KACvB,CACF,CAAC;MACH,CAAC,MAAM;QACLC,OAAO,CAACE,SAAS,CAAC9O,IAAI,CAACxE,WAAC,CAACe,SAAS,CAACkO,YAAY,CAAC,CAAC;MACnD;MAEA,MAAM,CAAC3N,OAAO,CAAC,GAAG9B,IAAI,CAACsB,WAAW,CAACsS,OAAO,CAAC;MAG3Cd,iBAAiB,GACfhR,OAAO,CAACT,GAAG,CAAC,QAAQ,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CACjCA,GAAG,CAAC,YAAY,CAAC;IACrB;EACF;EACA,IAAI,CAACuR,iBAAiB,IAAIC,aAAa,EAAE;IACvC7S,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACmD,IAAI,CACtBxE,WAAC,CAACuH,WAAW,CAAC,CAACvH,WAAC,CAAC+C,mBAAmB,CAACsP,aAAa,CAAC,CAAC,CACtD,CAAC;EACH;EAEA,IAAI;IAAEjR;EAAW,CAAC,GAAGmR,aAAa;EAClC,IACEnR,UAAU,KAERmB,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,GACvB;IACA,MAAM3D,EAAE,GAAGY,IAAI,CAACe,KAAK,CAACgT,qBAAqB,CAACnS,UAAU,CAAC;IACvD,IAAIxC,EAAE,EAAE;MACN2T,aAAa,CAACnR,UAAU,GAAGpB,WAAC,CAACgD,oBAAoB,CAAC,GAAG,EAAEpE,EAAE,EAAEwC,UAAU,CAAC;MACtEA,UAAU,GAAGxC,EAAE;IACjB;EACF;EAEA,MAAM4U,qBAAqB,GAAGxT,WAAC,CAACuH,WAAW,CAAC,EAAE,CAAC;EAC/CgL,aAAa,CAAClR,IAAI,CAACA,IAAI,CAACtC,OAAO,CAACyU,qBAAqB,CAAC;EACtD,MAAMC,aAAa,GAAGD,qBAAqB,CAACnS,IAAI;EAChD,IAAIyO,sBAAsB,CAAChR,MAAM,GAAG,CAAC,EAAE;IACrC,MAAMgT,QAAQ,GAAGQ,iBAAiB,CAACzR,GAAG,CAAC,WAAW,CAAC;IACnD,IAAI6S,kBAA6D;IACjE,KAAK,MAAMlU,IAAI,IAAIsS,QAAQ,EAAE;MAC3B,IACE,CAACtS,IAAI,CAACyQ,eAAe,CAAC,CAAC,IAAIzQ,IAAI,CAACkR,aAAa,CAAC,CAAC,KAC9ClR,IAAI,CAACE,IAAI,CAAmBsH,IAAI,KAAK,aAAa,EACnD;QACA0M,kBAAkB,GAAGlU,IAAI;QACzB;MACF;IACF;IACA,IAAIkU,kBAAkB,IAAI,IAAI,EAAE;MAE9BhI,oBAAoB,CAACgI,kBAAkB,CAAC;MACxCpP,+BAA+B,CAC7BwL,sBAAsB,EACtB4D,kBACF,CAAC;IACH,CAAC,MAAM;MAILnB,aAAa,CAAClR,IAAI,CAACA,IAAI,CAACtC,OAAO,CAC7BiB,WAAC,CAAC8B,aAAa,CACb9B,WAAC,CAACuB,kBAAkB,CAAC,CACnB,GAAGuO,sBAAsB,EACzB9P,WAAC,CAAC4L,aAAa,CAAC,GAAG,CAAC,CACrB,CAAC,EACF/J,SAAS,EACTA,SAAS,EACTA,SAAS,EACM,IAAI,EACN,IACf,CACF,CAAC;MACD4R,aAAa,CAACjP,IAAI,CAChBxE,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACsF,eAAe,CACf,QAAQ,EACRtF,WAAC,CAAC6C,gBAAgB,CAAC7C,WAAC,CAACyC,cAAc,CAAC,CAAC,EAAEzC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAC1D,CACF,CACF,CAAC;IACH;IACA4P,sBAAsB,GAAG,EAAE;EAC7B;EAEA2D,aAAa,CAACjP,IAAI,CAChBxE,WAAC,CAAC+C,mBAAmB,CACnB4Q,sBAAsB,CACpBzB,aAAa,EACbC,WAAW,EACXF,kBAAkB,GAAAnF,mBAAA,GAClB+C,kBAAkB,YAAA/C,mBAAA,GAAI9M,WAAC,CAACkJ,eAAe,CAAC0G,gBAAgB,CAAC,EACzD5P,WAAC,CAAC8I,cAAc,CAAC6G,oBAAoB,CAAC,EACtCQ,8BAA8B,GAAGD,uBAAuB,GAAG,IAAI,EAC/DvC,YAAY,EACZ3N,WAAC,CAACe,SAAS,CAACK,UAAU,CAAC,EACvB2J,KAAK,EACLxI,OACF,CACF,CACF,CAAC;EACD,IAAIkL,eAAe,EAAE;IACnBgG,aAAa,CAACjP,IAAI,CAChBxE,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACmG,cAAc,CAACnG,WAAC,CAACe,SAAS,CAAC0M,eAAe,CAAC,EAAE,CAACzN,WAAC,CAACyC,cAAc,CAAC,CAAC,CAAC,CACrE,CACF,CAAC;EACH;EACA,IAAI+P,cAAc,CAAC1T,MAAM,GAAG,CAAC,EAAE;IAC7B2U,aAAa,CAACjP,IAAI,CAChB,GAAGgO,cAAc,CAAC7L,GAAG,CAACC,IAAI,IAAI5G,WAAC,CAAC+C,mBAAmB,CAAC6D,IAAI,CAAC,CAC3D,CAAC;EACH;EAIApH,IAAI,CAACoU,YAAY,CAACxG,gBAAgB,CAACzG,GAAG,CAACC,IAAI,IAAI5G,WAAC,CAAC+C,mBAAmB,CAAC6D,IAAI,CAAC,CAAC,CAAC;EAE5E,IAAI8I,8BAA8B,EAAE;IAClC,MAAMmE,gBAAgB,GAAGjP,WAAW,CAACkP,UAAU,CAAC7E,YAAY,CAACtP,IAAI,CAAC;IAClE,IAAI,CAACkU,gBAAgB,CAACE,kBAAkB,CAACjV,MAAM,EAAE;MAE/CU,IAAI,CAACoU,YAAY,CACf5T,WAAC,CAACgU,mBAAmB,CAAC,KAAK,EAAE,CAC3BhU,WAAC,CAACiU,kBAAkB,CAACjU,WAAC,CAACe,SAAS,CAACkO,YAAY,CAAC,CAAC,CAChD,CACH,CAAC;IACH,CAAC,MAAM;MACL,MAAMiF,8BAA8B,GAAGtP,WAAW,CAAC5C,qBAAqB,CACtE,GAAG,GAAGiN,YAAY,CAACtP,IACrB,CAAC;MACD,MAAMwU,sBAAsB,GAAGlF,YAAY;MAC3CzP,IAAI,CAAC4U,mBAAmB,CAAC,CACvBpU,WAAC,CAACgU,mBAAmB,CAAC,KAAK,EAAE,CAC3BhU,WAAC,CAACiU,kBAAkB,CAACjU,WAAC,CAACe,SAAS,CAACoT,sBAAsB,CAAC,CAAC,EACzDnU,WAAC,CAACiU,kBAAkB,CAACC,8BAA8B,CAAC,CACrD,CAAC,EACFlU,WAAC,CAAC2C,cAAc,CAAC,CACf3C,WAAC,CAACgU,mBAAmB,CAAC,KAAK,EAAE,CAC3BhU,WAAC,CAACiU,kBAAkB,CAACjU,WAAC,CAACe,SAAS,CAACkO,YAAY,CAAC,CAAC,CAChD,CAAC,EAEFzP,IAAI,CAACE,IAAI,EACTM,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACgD,oBAAoB,CACpB,GAAG,EACHhD,WAAC,CAACe,SAAS,CAACmT,8BAA8B,CAAC,EAC3ClU,WAAC,CAACe,SAAS,CAACkO,YAAY,CAC1B,CACF,CAAC,CACF,CAAC,EACFjP,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACgD,oBAAoB,CACpB,GAAG,EACHhD,WAAC,CAACe,SAAS,CAACoT,sBAAsB,CAAC,EACnCnU,WAAC,CAACe,SAAS,CAACmT,8BAA8B,CAC5C,CACF,CAAC,CACF,CAAC;IACJ;EACF;EAEA,IAAI5H,uBAAuB,CAAC+H,IAAI,GAAG,CAAC,EAAE;IACpChI,6BAA6B,CAAC7M,IAAI,EAAE8M,uBAAuB,CAAC;EAC9D;EAGA9M,IAAI,CAACe,KAAK,CAAC+T,KAAK,CAAC,CAAC;EAElB,OAAO9U,IAAI;AACb;AAEA,SAASmU,sBAAsBA,CAC7BzB,aAA6B,EAC7BC,WAA2B,EAC3BF,kBAAoD,EACpDrC,gBAAkD,EAClDD,oBAAsC,EACtC4E,qBAA2C,EAC3C5G,YAAwD,EACxDvM,UAA+B,EAC/B2J,KAAiB,EACjBxI,OAA6B,EAC7B;EACA,IAAIiS,GAAG,EAAEC,GAAG;EACZ,MAAMzD,IAAoB,GAAG,CAC3BrD,YAAY,GACR7C,yBAAyB,CAACC,KAAK,EAAE4C,YAAY,CAAC,GAC9C3N,WAAC,CAACyC,cAAc,CAAC,CAAC,EACtBmN,gBAAgB,EAChBqC,kBAAkB,CACnB;EAEkC;IACjC,IAAI1P,OAAO,KAAK,SAAS,EAAE;MACzByO,IAAI,CAAC5K,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE6L,kBAAkB,EAAErC,gBAAgB,CAAC;IACzD;IACA,IACErN,OAAO,KAAK,SAAS,IACpBA,OAAO,KAAK,SAAS,IAAI,CAACwI,KAAK,CAAC2J,eAAe,CAAC,gBAAgB,CAAE,EACnE;MACAF,GAAG,GAAGxU,WAAC,CAAC2U,YAAY,CAAC,CAAC,GAAGzC,aAAa,EAAE,GAAGC,WAAW,CAAC,CAAC;MACxDsC,GAAG,GAAGzU,WAAC,CAACmG,cAAc,CACpB4E,KAAK,CAACC,SAAS,CAACzI,OAAO,KAAK,SAAS,GAAG,WAAW,GAAG,eAAe,CAAC,EACtEyO,IACF,CAAC;MACD,OAAOhR,WAAC,CAACgD,oBAAoB,CAAC,GAAG,EAAEwR,GAAG,EAAEC,GAAG,CAAC;IAC9C,CAAC,MAAM,IAAIlS,OAAO,KAAK,SAAS,EAAE;MAChCkS,GAAG,GAAGzU,WAAC,CAACmG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,gBAAgB,CAAC,EAAEgG,IAAI,CAAC;IACjE,CAAC,MAAM,IAAIzO,OAAO,KAAK,SAAS,EAAE;MAChC,IAAIgS,qBAAqB,EAAE;QACzBvD,IAAI,CAACxM,IAAI,CAAC2G,8BAA8B,CAACoJ,qBAAqB,CAAC,CAAC;MAClE;MACAE,GAAG,GAAGzU,WAAC,CAACmG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAEgG,IAAI,CAAC;IAChE,CAAC,MAAM,IAAIzO,OAAO,KAAK,SAAS,EAAE;MAChC,IACEgS,qBAAqB,IACrBnT,UAAU,IACVuO,oBAAoB,CAACjO,KAAK,KAAK,CAAC,EAChC;QACAsP,IAAI,CAACxM,IAAI,CAACmL,oBAAoB,CAAC;MACjC;MACA,IAAI4E,qBAAqB,EAAE;QACzBvD,IAAI,CAACxM,IAAI,CAAC2G,8BAA8B,CAACoJ,qBAAqB,CAAC,CAAC;MAClE,CAAC,MAAM,IAAInT,UAAU,EAAE;QACrB4P,IAAI,CAACxM,IAAI,CAACxE,WAAC,CAACsF,eAAe,CAAC,MAAM,EAAEtF,WAAC,CAAC8I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D;MACA,IAAI1H,UAAU,EAAE4P,IAAI,CAACxM,IAAI,CAACpD,UAAU,CAAC;MACrCqT,GAAG,GAAGzU,WAAC,CAACmG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAEgG,IAAI,CAAC;IAChE;EACF;EACA,IAAoCzO,OAAO,KAAK,SAAS,EAAE;IACzD,IACEgS,qBAAqB,IACrBnT,UAAU,IACVuO,oBAAoB,CAACjO,KAAK,KAAK,CAAC,EAChC;MACAsP,IAAI,CAACxM,IAAI,CAACmL,oBAAoB,CAAC;IACjC;IACA,IAAI4E,qBAAqB,EAAE;MACzBvD,IAAI,CAACxM,IAAI,CAAC2G,8BAA8B,CAACoJ,qBAAqB,CAAC,CAAC;IAClE,CAAC,MAAM,IAAInT,UAAU,EAAE;MACrB4P,IAAI,CAACxM,IAAI,CAACxE,WAAC,CAACsF,eAAe,CAAC,MAAM,EAAEtF,WAAC,CAAC8I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAI1H,UAAU,EAAE4P,IAAI,CAACxM,IAAI,CAACpD,UAAU,CAAC;IACrCqT,GAAG,GAAGzU,WAAC,CAACmG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAEgG,IAAI,CAAC;EAChE;EAIA,IAAIkB,aAAa,CAACpT,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAIqT,WAAW,CAACrT,MAAM,GAAG,CAAC,EAAE;MAC1B0V,GAAG,GAAGxU,WAAC,CAAC4U,aAAa,CAAC,CACpB5U,WAAC,CAAC6U,cAAc,CAAC7U,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAAC2U,YAAY,CAACzC,aAAa,CAAC,CAAC,EAClElS,WAAC,CAAC6U,cAAc,CAAC7U,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAAC2U,YAAY,CAACxC,WAAW,CAAC,CAAC,CACjE,CAAC;IACJ,CAAC,MAAM;MACLqC,GAAG,GAAGxU,WAAC,CAAC2U,YAAY,CAACzC,aAAa,CAAC;MAEnCuC,GAAG,GAAGzU,WAAC,CAAC6C,gBAAgB,CAAC4R,GAAG,EAAEzU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE;EACF,CAAC,MAAM;IAELsU,GAAG,GAAGxU,WAAC,CAAC2U,YAAY,CAACxC,WAAW,CAAC;IAEjCsC,GAAG,GAAGzU,WAAC,CAAC6C,gBAAgB,CAAC4R,GAAG,EAAEzU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE;EAEA,OAAOF,WAAC,CAACgD,oBAAoB,CAAC,GAAG,EAAEwR,GAAG,EAAEC,GAAG,CAAC;AAC9C;AAEA,SAASK,UAAUA,CACjBpV,IAAyE,EACzE;EACA,OAAOA,IAAI,CAACc,IAAI,KAAK,YAAY,GAC7Bd,IAAI,CAACC,IAAI,KAAK,WAAW,GACzBD,IAAI,CAACgC,KAAK,KAAK,WAAW;AAChC;AAEA,SAAS4M,WAAWA,CAAC5O,IAAuC,EAAE;EAC5D,OAAOA,IAAI,CAAC4I,UAAU,IAAI5I,IAAI,CAAC4I,UAAU,CAACxJ,MAAM,GAAG,CAAC;AACtD;AAEA,SAASiW,sBAAsBA,CAACrV,IAAkB,EAAE;EAClD,QAAQA,IAAI,CAACc,IAAI;IACf,KAAK,uBAAuB;MAC1B,OAAO,IAAI;IACb,KAAK,aAAa;IAClB,KAAK,eAAe;IACpB,KAAK,oBAAoB;IACzB,KAAK,sBAAsB;MACzB,OAAO8N,WAAW,CAAC5O,IAAI,CAAC;IAC1B;MACE,OAAO,KAAK;EAChB;AACF;AAEA,SAASsV,oBAAoBA,CAACtV,IAAa,EAAE;EAC3C,OAAO4O,WAAW,CAAC5O,IAAI,CAAC,IAAIA,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACqH,IAAI,CAACqM,sBAAsB,CAAC;AACzE;AAGA,SAASE,8BAA8BA,CACrCC,WAAwC,EACxCC,OASS,EACT;EACA,SAASC,sBAAsBA,CAC7BC,YAEC,EACD5T,GAAiB,EACjBsJ,KAAiB,EACe;IAChC,QAAQtJ,GAAG,CAACjB,IAAI;MACd,KAAK,eAAe;QAClB,OAAOR,WAAC,CAAC4L,aAAa,CAACnK,GAAG,CAACC,KAAK,CAAC;MACnC,KAAK,gBAAgB;MACrB,KAAK,eAAe;QAAE;UACpB,MAAM4T,QAAQ,GAAG7T,GAAG,CAACC,KAAK,GAAG,EAAE;UAC/B2T,YAAY,CAACxU,GAAG,CAAC,KAAK,CAAC,CAACC,WAAW,CAACd,WAAC,CAAC4L,aAAa,CAAC0J,QAAQ,CAAC,CAAC;UAC9D,OAAOtV,WAAC,CAAC4L,aAAa,CAAC0J,QAAQ,CAAC;QAClC;MACA;QAAS;UACP,MAAMC,GAAG,GAAGF,YAAY,CAAC9U,KAAK,CAACgT,qBAAqB,CAAC9R,GAAG,CAAC;UACzD4T,YAAY,CACTxU,GAAG,CAAC,KAAK,CAAC,CACVC,WAAW,CACVd,WAAC,CAACgD,oBAAoB,CACpB,GAAG,EACHuS,GAAG,EACHtK,uBAAuB,CAACF,KAAK,EAAEtJ,GAAG,CACpC,CACF,CAAC;UACH,OAAOzB,WAAC,CAACe,SAAS,CAACwU,GAAG,CAAC;QACzB;IACF;EACF;EACA,OAAO;IACLC,kBAAkBA,CAAChW,IAAI,EAAEuL,KAAK,EAAE;MAC9B,MAAMnM,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACd,EAAE;MACvB,IAAIA,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM6E,WAAW,GAAG,IAAA1B,oEAA2B,EAACnE,IAAI,CAACqB,GAAG,CAAC,MAAM,CAAC,CAAC;QACjE,IAAIqU,WAAW,CAAC7P,WAAW,CAAC,EAAE;UAC5B,MAAM1F,IAAI,GAAGf,EAAE,CAACe,IAAI;UACpBwV,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEpL,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IACD8V,oBAAoBA,CAACjW,IAAI,EAAEuL,KAAK,EAAE;MAChC,MAAMnM,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACyE,IAAI;MACzB,IAAIvF,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM6E,WAAW,GAAG,IAAA1B,oEAA2B,EAACnE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAIqU,WAAW,CAAC7P,WAAW,CAAC,EAAE;UAC5B,QAAQ7F,IAAI,CAACE,IAAI,CAACgW,QAAQ;YACxB,KAAK,GAAG;YACR,KAAK,KAAK;YACV,KAAK,KAAK;YACV,KAAK,KAAK;cACRP,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEnM,EAAE,CAACe,IAAI,CAAC;UACxC;QACF;MACF;IACF,CAAC;IACDgW,iBAAiBA,CAACnW,IAAI,EAAEuL,KAAK,EAAE;MAC7B,MAAMnM,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACyE,IAAI;MACzB,IAAIvF,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM6E,WAAW,GAAG,IAAA1B,oEAA2B,EAACnE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAIqU,WAAW,CAAC7P,WAAW,CAAC,EAAE;UAC5B,MAAM1F,IAAI,GAAGf,EAAE,CAACe,IAAI;UACpBwV,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEpL,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IAGDiW,gBAAgBA,CAACpW,IAAI,EAAEuL,KAAK,EAAE;MAC5B,KAAK,MAAMsK,YAAY,IAAI7V,IAAI,CAACqB,GAAG,CAAC,YAAY,CAAC,EAAE;QACjD,IAAI,CAACwU,YAAY,CAACQ,gBAAgB,CAAC,CAAC,EAAE;QACtC,MAAM;UAAEnW;QAAK,CAAC,GAAG2V,YAAY;QAC7B,MAAMzW,EAAE,GAAGc,IAAI,CAAC+B,GAAG;QACnB,MAAM4D,WAAW,GAAG,IAAA1B,oEAA2B,EAC7C0R,YAAY,CAACxU,GAAG,CAAC,OAAO,CAC1B,CAAC;QACD,IAAIqU,WAAW,CAAC7P,WAAW,CAAC,EAAE;UAC5B,IAAI,CAAC3F,IAAI,CAACiM,QAAQ,EAAE;YAElB,IAAI,CAACmJ,UAAU,CAAClW,EAAoC,CAAC,EAAE;cACrD,IAAIA,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;gBAC5B2U,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEnM,EAAE,CAACe,IAAI,CAAC;cACtC,CAAC,MAAM;gBACL,MAAMW,SAAS,GAAGN,WAAC,CAAC4L,aAAa,CAC9BhN,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;gBACDyT,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEzK,SAAS,CAAC;cACxC;YACF;UACF,CAAC,MAAM;YACL,MAAMiV,GAAG,GAAGH,sBAAsB,CAChCC,YAAY,EAEZzW,EAAE,EACFmM,KACF,CAAC;YACDoK,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEwK,GAAG,CAAC;UAClC;QACF;MACF;IACF,CAAC;IACD/G,oBAAoBA,CAAChP,IAAI,EAAEuL,KAAK,EAAE;MAChC,MAAM;QAAErL;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAM6F,WAAW,GAAG,IAAA1B,oEAA2B,EAACnE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAIqU,WAAW,CAAC7P,WAAW,CAAC,EAAE;QAC5B,MAAM/E,SAAS,GAAGN,WAAC,CAAC4L,aAAa,CAAC,GAAG,GAAGlM,IAAI,CAAC+B,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;QACzDwV,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEzK,SAAS,CAAC;MACxC;IACF,CAAC;IACDmO,qBAAqBA,CAACjP,IAAI,EAAEuL,KAAK,EAAE;MACjC,MAAM;QAAErL;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAMZ,EAAE,GAAGc,IAAI,CAAC+B,GAAG;MACnB,MAAM4D,WAAW,GAAG,IAAA1B,oEAA2B,EAACnE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAIqU,WAAW,CAAC7P,WAAW,CAAC,EAAE;QAC5B,IAAI,CAAC3F,IAAI,CAACiM,QAAQ,EAAE;UAClB,IAAI/M,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;YAC5B2U,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEnM,EAAE,CAACe,IAAI,CAAC;UACtC,CAAC,MAAM,IAAIf,EAAE,CAAC4B,IAAI,KAAK,aAAa,EAAE;YACpC,MAAMF,SAAS,GAAGN,WAAC,CAAC4L,aAAa,CAAC,GAAG,GAAGhN,EAAE,CAACA,EAAE,CAACe,IAAI,CAAC;YACnDwV,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEzK,SAAS,CAAC;UACxC,CAAC,MAAM;YACL,MAAMA,SAAS,GAAGN,WAAC,CAAC4L,aAAa,CAC9BhN,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;YACDyT,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEzK,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAMiV,GAAG,GAAGH,sBAAsB,CAChC5V,IAAI,EAEJZ,EAAE,EACFmM,KACF,CAAC;UACDoK,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEwK,GAAG,CAAC;QAClC;MACF;IACF,CAAC;IACDhH,aAAaA,CAAC/O,IAAI,EAAEuL,KAAK,EAAE;MACzB,MAAM;QAAErL;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAMZ,EAAE,GAAGc,IAAI,CAAC+B,GAAG;MACnB,MAAM4D,WAAW,GAAG,IAAA1B,oEAA2B,EAACnE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAIqU,WAAW,CAAC7P,WAAW,CAAC,EAAE;QAC5B,IAAI,CAAC3F,IAAI,CAACiM,QAAQ,EAAE;UAClB,IAAI/M,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;YAC5B2U,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEnM,EAAE,CAACe,IAAI,CAAC;UACtC,CAAC,MAAM;YACL,MAAMW,SAAS,GAAGN,WAAC,CAAC4L,aAAa,CAC9BhN,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;YACDyT,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEzK,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAMiV,GAAG,GAAGH,sBAAsB,CAAC5V,IAAI,EAAEZ,EAAE,EAAEmM,KAAK,CAAC;UACnDoK,OAAO,CAAC9P,WAAW,EAAE0F,KAAK,EAAEwK,GAAG,CAAC;QAClC;MACF;IACF;EACF,CAAC;AACH;AAEA,SAASO,mCAAmCA,CAACtW,IAAc,EAAE;EAC3D,OACEA,IAAI,CAACuW,iBAAiB,CAAC;IAAEnX,EAAE,EAAE;EAAK,CAAC,CAAC,IAAIoW,oBAAoB,CAACxV,IAAI,CAACE,IAAI,CAAC;AAE3E;AAEA,SAASsB,wBAAwBA,CAACT,KAAY,EAAEZ,IAAY,EAAE;EAC5D,MAAMf,EAAE,GAAG2B,KAAK,CAACyB,qBAAqB,CAACrC,IAAI,CAAC;EAC5CY,KAAK,CAACiE,IAAI,CAAC;IAAE5F,EAAE;IAAEoI,IAAI,EAAE;EAAM,CAAC,CAAC;EAC/B,OAAOhH,WAAC,CAACe,SAAS,CAACnC,EAAE,CAAC;AACxB;AAEe,SAAAoX,SACb;EAAEC,aAAa;EAAEC;AAAsB,CAAC,EACxC;EAAEC;AAAe,CAAC,EAClB5T,OAA6B,EAC7B6T,QAAkC,EACpB;EAAA,IAAAC,WAAA,EAAAC,YAAA;EAGP;IACL,IACE/T,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,EACrB;MACA0T,aAAa,CAAkB,SAAU,CAAC;IAC5C,CAAC,MAAM,IAAI1T,OAAO,KAAK,SAAS,EAAE;MAChC0T,aAAa,CAAkB,SAAU,CAAC;IAC5C,CAAC,MAAM;MACLA,aAAa,CAAkB,SAAU,CAAC;IAC5C;EACF;EAEA,MAAMM,OAAO,GAAG,IAAIC,OAAO,CAAW,CAAC;EACvC,MAAM9J,aAAa,IAAA2J,WAAA,GAAGH,UAAU,CAAC,eAAe,CAAC,YAAAG,WAAA,GAAIF,KAAK;EAC1D,MAAMxJ,oBAAoB,IAAA2J,YAAA,GAAGJ,UAAU,CAAC,sBAAsB,CAAC,YAAAI,YAAA,GAAIH,KAAK;EAExE,MAAMM,sBAA2C,GAC/CxB,8BAA8B,CAC5Ba,mCAAmC,EACnCY,UACF,CAAC;EAEH,SAASA,UAAUA,CACjBlX,IAAuB,EACvBuL,KAAiB,EACjBzK,SAA8D,EAC9D;IAAA,IAAAqW,UAAA,EAAAC,QAAA;IACA,IAAIL,OAAO,CAACxW,GAAG,CAACP,IAAI,CAAC,EAAE;IACvB,MAAM;MAAEE;IAAK,CAAC,GAAGF,IAAI;IACrB,CAAAmX,UAAA,GAAArW,SAAS,YAAAqW,UAAA,GAATrW,SAAS,IAAAsW,QAAA,GAAKlX,IAAI,CAACd,EAAE,qBAAPgY,QAAA,CAASjX,IAAI;IAC3B,MAAM2B,OAAO,GAAGmL,cAAc,CAC5BjN,IAAI,EACJuL,KAAK,EACL2B,aAAa,EACbC,oBAAoB,EACpBrM,SAAS,EACTmW,sBAAsB,EACtBlU,OACF,CAAC;IACD,IAAIjB,OAAO,EAAE;MACXiV,OAAO,CAAC9W,GAAG,CAAC6B,OAAO,CAAC;MACpB;IACF;IACAiV,OAAO,CAAC9W,GAAG,CAACD,IAAI,CAAC;EACnB;EAEA,OAAO;IACLG,IAAI,EAAE,qBAAqB;IAC3ByW,QAAQ,EAAEA,QAAQ;IAElBjB,OAAO,EAAA0B,MAAA,CAAAC,MAAA;MACLC,wBAAwBA,CAACvX,IAAI,EAAEuL,KAAK,EAAE;QACpC,MAAM;UAAEiM;QAAY,CAAC,GAAGxX,IAAI,CAACE,IAAI;QACjC,IACE,CAAAsX,WAAW,oBAAXA,WAAW,CAAExW,IAAI,MAAK,kBAAkB,IAGxC8N,WAAW,CAAC0I,WAAW,CAAC,EACxB;UACA,MAAM9B,WAAW,GAAG,CAAC8B,WAAW,CAACpY,EAAE;UAC8B;YAAA,IAAAqY,qBAAA;YAE/D,CAAAA,qBAAA,GAAAzX,IAAI,CAAC0X,sBAAsB,YAAAD,qBAAA,GAA3BzX,IAAI,CAAC0X,sBAAsB,GAEzB5Y,OAAO,CAAC,iBAAiB,CAAC,CAAC6Y,QAAQ,CAACC,SAAS,CAACF,sBAAsB;UACxE;UACA,MAAMG,yBAAyB,GAC7B7X,IAAI,CAAC0X,sBAAsB,CAAC,CAAiC;UAC/D,IAAIhC,WAAW,EAAE;YACfwB,UAAU,CACRW,yBAAyB,EACzBtM,KAAK,EACL/K,WAAC,CAAC4L,aAAa,CAAC,SAAS,CAC3B,CAAC;UACH;QACF;MACF,CAAC;MACD0L,sBAAsBA,CAAC9X,IAAI,EAAE;QAC3B,MAAM;UAAEwX;QAAY,CAAC,GAAGxX,IAAI,CAACE,IAAI;QACjC,IACE,CAAAsX,WAAW,oBAAXA,WAAW,CAAExW,IAAI,MAAK,kBAAkB,IAGxC8N,WAAW,CAAC0I,WAAW,CAAC,EACxB;UACiE;YAAA,IAAAO,sBAAA;YAE/D,CAAAA,sBAAA,GAAA/X,IAAI,CAAC0X,sBAAsB,YAAAK,sBAAA,GAA3B/X,IAAI,CAAC0X,sBAAsB,GAEzB5Y,OAAO,CAAC,iBAAiB,CAAC,CAAC6Y,QAAQ,CAACC,SAAS,CAACF,sBAAsB;UACxE;UACA1X,IAAI,CAAC0X,sBAAsB,CAAC,CAAC;QAC/B;MACF,CAAC;MAEDM,KAAKA,CAAChY,IAAI,EAAEuL,KAAK,EAAE;QACjB2L,UAAU,CAAClX,IAAI,EAAEuL,KAAK,EAAElJ,SAAS,CAAC;MACpC;IAAC,GAEE4U,sBAAsB;EAE7B,CAAC;AACH", "ignoreList": []}