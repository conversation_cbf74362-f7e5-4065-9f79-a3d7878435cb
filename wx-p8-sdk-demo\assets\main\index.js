window.__require = function e(t, n, r) {
    function s(o, u) {
        if (!n[o]) {
            if (!t[o]) {
                var b = o.split("/");
                b = b[b.length - 1];
                if (!t[b]) {
                    var a = "function" == typeof __require && __require;
                    if (!u && a) return a(b, !0);
                    if (i) return i(b, !0);
                    throw new Error("Cannot find module '" + o + "'");
                }
                o = b;
            }
            var f = n[o] = {
                exports: {}
            };
            t[o][0].call(f.exports, function(e) {
                return s(t[o][1][e] || e);
            }, f, f.exports, e, t, n, r);
        }
        return n[o].exports;
    }
    for (var i = "function" == typeof __require && __require, o = 0; o < r.length; o++) s(r[o]);
    return s;
}({
    scprit1: [ function(require, module, exports) {
        "use strict";
        cc._RF.push(module, "a2b01KDui5NBJsCrrS1EThq", "scprit1");
        var __extends = this && this.__extends || function() {
            var extendStatics = function(d, b) {
                return (extendStatics = Object.setPrototypeOf || {
                    __proto__: []
                } instanceof Array && function(d, b) {
                    d.__proto__ = b;
                } || function(d, b) {
                    for (var p in b) Object.prototype.hasOwnProperty.call(b, p) && (d[p] = b[p]);
                })(d, b);
            };
            return function(d, b) {
                extendStatics(d, b);
                function __() {
                    this.constructor = d;
                }
                d.prototype = null === b ? Object.create(b) : (__.prototype = b.prototype, new __());
            };
        }(), __decorate = this && this.__decorate || function(decorators, target, key, desc) {
            var d, c = arguments.length, r = c < 3 ? target : null === desc ? desc = Object.getOwnPropertyDescriptor(target, key) : desc;
            if ("object" === typeof Reflect && "function" === typeof Reflect.decorate) r = Reflect.decorate(decorators, target, key, desc); else for (var i = decorators.length - 1; i >= 0; i--) (d = decorators[i]) && (r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r);
            return c > 3 && r && Object.defineProperty(target, key, r), r;
        };
        Object.defineProperty(exports, "__esModule", {
            value: !0
        });
        var _a = cc._decorator, ccclass = _a.ccclass, script1 = (_a.property, function(_super) {
            __extends(script1, _super);
            function script1() {
                return null !== _super && _super.apply(this, arguments) || this;
            }
            script1.prototype.createWhite = function() {
                var texture = new cc.Texture2D(), image = new Uint8Array([ 255, 255, 255, 255 ]);
                texture.initWithData(image, cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
                var spriteFrame = new cc.SpriteFrame();
                spriteFrame.setTexture(texture);
                return spriteFrame;
            };
            script1.prototype.createNode = function(name, parent, set) {
                void 0 === parent && (parent = null);
                void 0 === set && (set = null);
                var node = new cc.Node(name);
                if (parent) node.parent = parent; else {
                    var rootNode = cc.find("Canvas");
                    node.parent = rootNode;
                }
                set && Object(set) === set && Object.assign(node, set);
                node.zIndex = cc.macro.MAX_ZINDEX;
                return node;
            };
            script1.prototype.addComponents = function(node, type, set) {
                void 0 === set && (set = null);
                var cmp = node.addComponent(type);
                set && Object(set) === set && Object.assign(cmp, set);
                return cmp;
            };
            script1.prototype.hexToColor = function(hex) {
                var color = new cc.Color();
                color.r = parseInt(hex.substr(1, 2), 16);
                color.g = parseInt(hex.substr(3, 2), 16);
                color.b = parseInt(hex.substr(5, 2), 16);
                return color;
            };
            return script1 = __decorate([ ccclass ], script1);
        }(cc.Component));
        exports.default = script1;
        cc._RF.pop();
    }, {} ]
}, {}, [ "scprit1" ]);