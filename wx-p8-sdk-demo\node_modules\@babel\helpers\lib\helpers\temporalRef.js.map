{"version": 3, "names": ["_temporalUndefined", "require", "_tdz", "_temporalRef", "val", "name", "undef", "err"], "sources": ["../../src/helpers/temporalRef.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport undef from \"./temporalUndefined.ts\";\nimport err from \"./tdz.ts\";\n\nexport default function _temporalRef<T>(val: T, name: string) {\n  return val === undef ? err(name) : val;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;AAEe,SAASE,YAAYA,CAAIC,GAAM,EAAEC,IAAY,EAAE;EAC5D,OAAOD,GAAG,KAAKE,0BAAK,GAAG,IAAAC,YAAG,EAACF,IAAI,CAAC,GAAGD,GAAG;AACxC", "ignoreList": []}