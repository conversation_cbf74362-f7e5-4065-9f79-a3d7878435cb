{"version": 3, "names": ["_template", "require", "_t", "blockStatement", "callExpression", "functionExpression", "isAssignmentPattern", "isFunctionDeclaration", "isRestElement", "returnStatement", "isCallExpression", "buildAnonymousExpressionWrapper", "template", "expression", "buildNamedExpressionWrapper", "buildDeclarationWrapper", "statements", "classOrObjectMethod", "path", "callId", "node", "body", "container", "async", "generator", "get", "unwrapFunctionEnvironment", "plainFunction", "inPath", "noNewArrows", "ignoreFunctionLength", "<PERSON><PERSON><PERSON>", "functionId", "nodeParams", "params", "isArrowFunctionExpression", "_path$arrowFunctionTo", "arrowFunctionToExpression", "isDeclaration", "built", "id", "type", "param", "push", "scope", "generateUidIdentifier", "wrapperArgs", "NAME", "REF", "name", "FUNCTION", "PARAMS", "replaceWith", "insertAfter", "length", "wrapFunction", "isMethod", "_path", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport template from \"@babel/template\";\nimport {\n  blockStatement,\n  callExpression,\n  functionExpression,\n  isAssignmentPattern,\n  isFunctionDeclaration,\n  isRestElement,\n  returnStatement,\n  isCallExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\ntype ExpressionWrapperBuilder<ExtraBody extends t.Node[]> = (\n  replacements?: Parameters<ReturnType<typeof template.expression>>[0],\n) => t.CallExpression & {\n  callee: t.FunctionExpression & {\n    body: {\n      body: [\n        t.VariableDeclaration & {\n          declarations: [\n            { init: t.FunctionExpression | t.ArrowFunctionExpression },\n          ];\n        },\n        ...ExtraBody,\n      ];\n    };\n  };\n};\n\nconst buildAnonymousExpressionWrapper = template.expression(`\n  (function () {\n    var REF = FUNCTION;\n    return function NAME(PARAMS) {\n      return REF.apply(this, arguments);\n    };\n  })()\n`) as ExpressionWrapperBuilder<\n  [t.ReturnStatement & { argument: t.FunctionExpression }]\n>;\n\nconst buildNamedExpressionWrapper = template.expression(`\n  (function () {\n    var REF = FUNCTION;\n    function NAME(PARAMS) {\n      return REF.apply(this, arguments);\n    }\n    return NAME;\n  })()\n`) as ExpressionWrapperBuilder<\n  [t.FunctionDeclaration, t.ReturnStatement & { argument: t.Identifier }]\n>;\n\nconst buildDeclarationWrapper = template.statements(`\n  function NAME(PARAMS) { return REF.apply(this, arguments); }\n  function REF() {\n    REF = FUNCTION;\n    return REF.apply(this, arguments);\n  }\n`);\n\nfunction classOrObjectMethod(\n  path: NodePath<t.ClassMethod | t.ClassPrivateMethod | t.ObjectMethod>,\n  callId: t.Expression,\n) {\n  const node = path.node;\n  const body = node.body;\n\n  const container = functionExpression(\n    null,\n    [],\n    blockStatement(body.body),\n    true,\n  );\n  body.body = [\n    returnStatement(callExpression(callExpression(callId, [container]), [])),\n  ];\n\n  // Regardless of whether or not the wrapped function is a an async method\n  // or generator the outer function should not be\n  node.async = false;\n  node.generator = false;\n\n  // Unwrap the wrapper IIFE's environment so super and this and such still work.\n  (\n    path.get(\"body.body.0.argument.callee.arguments.0\") as NodePath\n  ).unwrapFunctionEnvironment();\n}\n\nfunction plainFunction(\n  inPath: NodePath<Exclude<t.Function, t.Method>>,\n  callId: t.Expression,\n  noNewArrows: boolean,\n  ignoreFunctionLength: boolean,\n  hadName: boolean,\n) {\n  let path: NodePath<\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.CallExpression\n    | t.ArrowFunctionExpression\n  > = inPath;\n  let node;\n  let functionId = null;\n  const nodeParams = inPath.node.params;\n\n  if (path.isArrowFunctionExpression()) {\n    if (process.env.BABEL_8_BREAKING) {\n      path = path.arrowFunctionToExpression({ noNewArrows });\n    } else {\n      // arrowFunctionToExpression returns undefined in @babel/traverse < 7.18.10\n      path = path.arrowFunctionToExpression({ noNewArrows }) ?? path;\n    }\n    node = path.node as\n      | t.FunctionDeclaration\n      | t.FunctionExpression\n      | t.CallExpression;\n  } else {\n    node = path.node;\n  }\n\n  const isDeclaration = isFunctionDeclaration(node);\n\n  let built = node;\n  if (!isCallExpression(node)) {\n    functionId = node.id;\n    node.id = null;\n    node.type = \"FunctionExpression\";\n    built = callExpression(callId, [\n      node as Exclude<typeof node, t.FunctionDeclaration>,\n    ]);\n  }\n\n  const params: t.Identifier[] = [];\n  for (const param of nodeParams) {\n    if (isAssignmentPattern(param) || isRestElement(param)) {\n      break;\n    }\n    params.push(path.scope.generateUidIdentifier(\"x\"));\n  }\n\n  const wrapperArgs = {\n    NAME: functionId || null,\n    // TODO: Use `functionId` rather than `hadName` for the condition\n    REF: path.scope.generateUidIdentifier(hadName ? functionId.name : \"ref\"),\n    FUNCTION: built,\n    PARAMS: params,\n  };\n\n  if (isDeclaration) {\n    const container = buildDeclarationWrapper(wrapperArgs);\n    path.replaceWith(container[0]);\n    path.insertAfter(container[1]);\n  } else {\n    let container;\n\n    if (hadName) {\n      container = buildNamedExpressionWrapper(wrapperArgs);\n    } else {\n      container = buildAnonymousExpressionWrapper(wrapperArgs);\n    }\n\n    if (functionId || (!ignoreFunctionLength && params.length)) {\n      path.replaceWith(container);\n    } else {\n      // we can omit this wrapper as the conditions it protects for do not apply\n      path.replaceWith(built);\n    }\n  }\n}\n\nexport default function wrapFunction(\n  path: NodePath<t.Function>,\n  callId: t.Expression,\n  // TODO(Babel 8): Consider defaulting to false for spec compliance\n  noNewArrows: boolean = true,\n  ignoreFunctionLength: boolean = false,\n) {\n  if (path.isMethod()) {\n    classOrObjectMethod(path, callId);\n  } else {\n    const hadName = \"id\" in path.node && !!path.node.id;\n    if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n      // polyfill when being run by an older Babel version\n      path.ensureFunctionName ??=\n        // eslint-disable-next-line no-restricted-globals\n        require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n    }\n    // @ts-expect-error It is invalid to call this on an arrow expression,\n    // but we'll convert it to a function expression anyway.\n    path = path.ensureFunctionName(false);\n    plainFunction(\n      path as NodePath<Exclude<t.Function, t.Method>>,\n      callId,\n      noNewArrows,\n      ignoreFunctionLength,\n      hadName,\n    );\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AASsB;EARpBE,cAAc;EACdC,cAAc;EACdC,kBAAkB;EAClBC,mBAAmB;EACnBC,qBAAqB;EACrBC,aAAa;EACbC,eAAe;EACfC;AAAgB,IAAAR,EAAA;AAqBlB,MAAMS,+BAA+B,GAAGC,iBAAQ,CAACC,UAAU,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAEA;AAED,MAAMC,2BAA2B,GAAGF,iBAAQ,CAACC,UAAU,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAEA;AAED,MAAME,uBAAuB,GAAGH,iBAAQ,CAACI,UAAU,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,SAASC,mBAAmBA,CAC1BC,IAAqE,EACrEC,MAAoB,EACpB;EACA,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACtB,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI;EAEtB,MAAMC,SAAS,GAAGjB,kBAAkB,CAClC,IAAI,EACJ,EAAE,EACFF,cAAc,CAACkB,IAAI,CAACA,IAAI,CAAC,EACzB,IACF,CAAC;EACDA,IAAI,CAACA,IAAI,GAAG,CACVZ,eAAe,CAACL,cAAc,CAACA,cAAc,CAACe,MAAM,EAAE,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CACzE;EAIDF,IAAI,CAACG,KAAK,GAAG,KAAK;EAClBH,IAAI,CAACI,SAAS,GAAG,KAAK;EAIpBN,IAAI,CAACO,GAAG,CAAC,yCAAyC,CAAC,CACnDC,yBAAyB,CAAC,CAAC;AAC/B;AAEA,SAASC,aAAaA,CACpBC,MAA+C,EAC/CT,MAAoB,EACpBU,WAAoB,EACpBC,oBAA6B,EAC7BC,OAAgB,EAChB;EACA,IAAIb,IAKH,GAAGU,MAAM;EACV,IAAIR,IAAI;EACR,IAAIY,UAAU,GAAG,IAAI;EACrB,MAAMC,UAAU,GAAGL,MAAM,CAACR,IAAI,CAACc,MAAM;EAErC,IAAIhB,IAAI,CAACiB,yBAAyB,CAAC,CAAC,EAAE;IAG7B;MAAA,IAAAC,qBAAA;MAELlB,IAAI,IAAAkB,qBAAA,GAAGlB,IAAI,CAACmB,yBAAyB,CAAC;QAAER;MAAY,CAAC,CAAC,YAAAO,qBAAA,GAAIlB,IAAI;IAChE;IACAE,IAAI,GAAGF,IAAI,CAACE,IAGQ;EACtB,CAAC,MAAM;IACLA,IAAI,GAAGF,IAAI,CAACE,IAAI;EAClB;EAEA,MAAMkB,aAAa,GAAG/B,qBAAqB,CAACa,IAAI,CAAC;EAEjD,IAAImB,KAAK,GAAGnB,IAAI;EAChB,IAAI,CAACV,gBAAgB,CAACU,IAAI,CAAC,EAAE;IAC3BY,UAAU,GAAGZ,IAAI,CAACoB,EAAE;IACpBpB,IAAI,CAACoB,EAAE,GAAG,IAAI;IACdpB,IAAI,CAACqB,IAAI,GAAG,oBAAoB;IAChCF,KAAK,GAAGnC,cAAc,CAACe,MAAM,EAAE,CAC7BC,IAAI,CACL,CAAC;EACJ;EAEA,MAAMc,MAAsB,GAAG,EAAE;EACjC,KAAK,MAAMQ,KAAK,IAAIT,UAAU,EAAE;IAC9B,IAAI3B,mBAAmB,CAACoC,KAAK,CAAC,IAAIlC,aAAa,CAACkC,KAAK,CAAC,EAAE;MACtD;IACF;IACAR,MAAM,CAACS,IAAI,CAACzB,IAAI,CAAC0B,KAAK,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA,MAAMC,WAAW,GAAG;IAClBC,IAAI,EAAEf,UAAU,IAAI,IAAI;IAExBgB,GAAG,EAAE9B,IAAI,CAAC0B,KAAK,CAACC,qBAAqB,CAACd,OAAO,GAAGC,UAAU,CAACiB,IAAI,GAAG,KAAK,CAAC;IACxEC,QAAQ,EAAEX,KAAK;IACfY,MAAM,EAAEjB;EACV,CAAC;EAED,IAAII,aAAa,EAAE;IACjB,MAAMhB,SAAS,GAAGP,uBAAuB,CAAC+B,WAAW,CAAC;IACtD5B,IAAI,CAACkC,WAAW,CAAC9B,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9BJ,IAAI,CAACmC,WAAW,CAAC/B,SAAS,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,MAAM;IACL,IAAIA,SAAS;IAEb,IAAIS,OAAO,EAAE;MACXT,SAAS,GAAGR,2BAA2B,CAACgC,WAAW,CAAC;IACtD,CAAC,MAAM;MACLxB,SAAS,GAAGX,+BAA+B,CAACmC,WAAW,CAAC;IAC1D;IAEA,IAAId,UAAU,IAAK,CAACF,oBAAoB,IAAII,MAAM,CAACoB,MAAO,EAAE;MAC1DpC,IAAI,CAACkC,WAAW,CAAC9B,SAAS,CAAC;IAC7B,CAAC,MAAM;MAELJ,IAAI,CAACkC,WAAW,CAACb,KAAK,CAAC;IACzB;EACF;AACF;AAEe,SAASgB,YAAYA,CAClCrC,IAA0B,EAC1BC,MAAoB,EAEpBU,WAAoB,GAAG,IAAI,EAC3BC,oBAA6B,GAAG,KAAK,EACrC;EACA,IAAIZ,IAAI,CAACsC,QAAQ,CAAC,CAAC,EAAE;IACnBvC,mBAAmB,CAACC,IAAI,EAAEC,MAAM,CAAC;EACnC,CAAC,MAAM;IACL,MAAMY,OAAO,GAAG,IAAI,IAAIb,IAAI,CAACE,IAAI,IAAI,CAAC,CAACF,IAAI,CAACE,IAAI,CAACoB,EAAE;IACc;MAAA,IAAAiB,KAAA,EAAAC,qBAAA;MAE/D,CAAAA,qBAAA,IAAAD,KAAA,GAAAvC,IAAI,EAACyC,kBAAkB,YAAAD,qBAAA,GAAvBD,KAAA,CAAKE,kBAAkB,GAErB1D,OAAO,CAAC,iBAAiB,CAAC,CAAC2D,QAAQ,CAACC,SAAS,CAACF,kBAAkB;IACpE;IAGAzC,IAAI,GAAGA,IAAI,CAACyC,kBAAkB,CAAC,KAAK,CAAC;IACrChC,aAAa,CACXT,IAAI,EACJC,MAAM,EACNU,WAAW,EACXC,oBAAoB,EACpBC,OACF,CAAC;EACH;AACF", "ignoreList": []}