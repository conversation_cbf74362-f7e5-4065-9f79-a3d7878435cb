{"version": 3, "names": ["_debug", "data", "require", "_path", "_async", "_moduleTypes", "_url", "_importMetaResolve", "_fs", "debug", "buildDebug", "EXACT_RE", "BABEL_PLUGIN_PREFIX_RE", "BABEL_PRESET_PREFIX_RE", "BABEL_PLUGIN_ORG_RE", "BABEL_PRESET_ORG_RE", "OTHER_PLUGIN_ORG_RE", "OTHER_PRESET_ORG_RE", "OTHER_ORG_DEFAULT_RE", "resolvePlugin", "exports", "resolveStandardizedName", "bind", "resolvePreset", "loadPlugin", "name", "dirname", "filepath", "loader", "isAsync", "value", "requireModule", "loadPreset", "standardizeName", "type", "path", "isAbsolute", "isPreset", "replace", "resolveAlternativesHelper", "standardizedName", "error", "code", "message", "oppositeType", "transformName", "tryRequireResolve", "id", "v", "w", "split", "process", "versions", "node", "resolve", "r", "paths", "b", "M", "f", "_findPath", "_nodeModulePaths", "concat", "Error", "tryImportMetaResolve", "options", "importMetaResolve", "resolveStandardizedNameForRequire", "it", "res", "next", "done", "resolveStandardizedNameForImport", "parentUrl", "pathToFileURL", "join", "href", "fileURLToPath", "allowAsync", "supportsESM", "resolved", "existsSync", "Object", "assign", "e", "e2", "LOADING_MODULES", "Set", "has", "add", "loadCodeDefault", "err", "delete"], "sources": ["../../../src/config/files/plugins.ts"], "sourcesContent": ["/**\n * This file handles all logic for converting string-based configuration references into loaded objects.\n */\n\nimport buildDebug from \"debug\";\nimport path from \"path\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { isAsync } from \"../../gensync-utils/async.ts\";\nimport loadCodeDefault, { supportsESM } from \"./module-types.ts\";\nimport { fileURLToPath, pathToFileURL } from \"url\";\n\nimport { resolve as importMetaResolve } from \"../../vendor/import-meta-resolve.js\";\n\nimport { createRequire } from \"module\";\nimport { existsSync } from \"fs\";\nconst require = createRequire(import.meta.url);\n\nconst debug = buildDebug(\"babel:config:loading:files:plugins\");\n\nconst EXACT_RE = /^module:/;\nconst BABEL_PLUGIN_PREFIX_RE = /^(?!@|module:|[^/]+\\/|babel-plugin-)/;\nconst BABEL_PRESET_PREFIX_RE = /^(?!@|module:|[^/]+\\/|babel-preset-)/;\nconst BABEL_PLUGIN_ORG_RE = /^(@babel\\/)(?!plugin-|[^/]+\\/)/;\nconst BABEL_PRESET_ORG_RE = /^(@babel\\/)(?!preset-|[^/]+\\/)/;\nconst OTHER_PLUGIN_ORG_RE =\n  /^(@(?!babel\\/)[^/]+\\/)(?![^/]*babel-plugin(?:-|\\/|$)|[^/]+\\/)/;\nconst OTHER_PRESET_ORG_RE =\n  /^(@(?!babel\\/)[^/]+\\/)(?![^/]*babel-preset(?:-|\\/|$)|[^/]+\\/)/;\nconst OTHER_ORG_DEFAULT_RE = /^(@(?!babel$)[^/]+)$/;\n\nexport const resolvePlugin = resolveStandardizedName.bind(null, \"plugin\");\nexport const resolvePreset = resolveStandardizedName.bind(null, \"preset\");\n\nexport function* loadPlugin(\n  name: string,\n  dirname: string,\n): Handler<{ filepath: string; value: unknown }> {\n  const { filepath, loader } = resolvePlugin(name, dirname, yield* isAsync());\n\n  const value = yield* requireModule(\"plugin\", loader, filepath);\n  debug(\"Loaded plugin %o from %o.\", name, dirname);\n\n  return { filepath, value };\n}\n\nexport function* loadPreset(\n  name: string,\n  dirname: string,\n): Handler<{ filepath: string; value: unknown }> {\n  const { filepath, loader } = resolvePreset(name, dirname, yield* isAsync());\n\n  const value = yield* requireModule(\"preset\", loader, filepath);\n\n  debug(\"Loaded preset %o from %o.\", name, dirname);\n\n  return { filepath, value };\n}\n\nfunction standardizeName(type: \"plugin\" | \"preset\", name: string) {\n  // Let absolute and relative paths through.\n  if (path.isAbsolute(name)) return name;\n\n  const isPreset = type === \"preset\";\n\n  return (\n    name\n      // foo -> babel-preset-foo\n      .replace(\n        isPreset ? BABEL_PRESET_PREFIX_RE : BABEL_PLUGIN_PREFIX_RE,\n        `babel-${type}-`,\n      )\n      // @babel/es2015 -> @babel/preset-es2015\n      .replace(\n        isPreset ? BABEL_PRESET_ORG_RE : BABEL_PLUGIN_ORG_RE,\n        `$1${type}-`,\n      )\n      // @foo/mypreset -> @foo/babel-preset-mypreset\n      .replace(\n        isPreset ? OTHER_PRESET_ORG_RE : OTHER_PLUGIN_ORG_RE,\n        `$1babel-${type}-`,\n      )\n      // @foo -> @foo/babel-preset\n      .replace(OTHER_ORG_DEFAULT_RE, `$1/babel-${type}`)\n      // module:mypreset -> mypreset\n      .replace(EXACT_RE, \"\")\n  );\n}\n\ntype Result<T> = { error: Error; value: null } | { error: null; value: T };\n\nfunction* resolveAlternativesHelper(\n  type: \"plugin\" | \"preset\",\n  name: string,\n): Iterator<string, string, Result<string>> {\n  const standardizedName = standardizeName(type, name);\n  const { error, value } = yield standardizedName;\n  if (!error) return value;\n\n  // @ts-expect-error code may not index error\n  if (error.code !== \"MODULE_NOT_FOUND\") throw error;\n\n  if (standardizedName !== name && !(yield name).error) {\n    error.message += `\\n- If you want to resolve \"${name}\", use \"module:${name}\"`;\n  }\n\n  if (!(yield standardizeName(type, \"@babel/\" + name)).error) {\n    error.message += `\\n- Did you mean \"@babel/${name}\"?`;\n  }\n\n  const oppositeType = type === \"preset\" ? \"plugin\" : \"preset\";\n  if (!(yield standardizeName(oppositeType, name)).error) {\n    error.message += `\\n- Did you accidentally pass a ${oppositeType} as a ${type}?`;\n  }\n\n  if (type === \"plugin\") {\n    const transformName = standardizedName.replace(\"-proposal-\", \"-transform-\");\n    if (transformName !== standardizedName && !(yield transformName).error) {\n      error.message += `\\n- Did you mean \"${transformName}\"?`;\n    }\n  }\n\n  error.message += `\\n\nMake sure that all the Babel plugins and presets you are using\nare defined as dependencies or devDependencies in your package.json\nfile. It's possible that the missing plugin is loaded by a preset\nyou are using that forgot to add the plugin to its dependencies: you\ncan workaround this problem by explicitly adding the missing package\nto your top-level package.json.\n`;\n\n  throw error;\n}\n\nfunction tryRequireResolve(\n  id: string,\n  dirname: string | undefined,\n): Result<string> {\n  try {\n    if (dirname) {\n      return { error: null, value: require.resolve(id, { paths: [dirname] }) };\n    } else {\n      return { error: null, value: require.resolve(id) };\n    }\n  } catch (error) {\n    return { error, value: null };\n  }\n}\n\nfunction tryImportMetaResolve(\n  id: Parameters<typeof importMetaResolve>[0],\n  options: Parameters<typeof importMetaResolve>[1],\n): Result<string> {\n  try {\n    return { error: null, value: importMetaResolve(id, options) };\n  } catch (error) {\n    return { error, value: null };\n  }\n}\n\nfunction resolveStandardizedNameForRequire(\n  type: \"plugin\" | \"preset\",\n  name: string,\n  dirname: string,\n) {\n  const it = resolveAlternativesHelper(type, name);\n  let res = it.next();\n  while (!res.done) {\n    res = it.next(tryRequireResolve(res.value, dirname));\n  }\n  return { loader: \"require\" as const, filepath: res.value };\n}\nfunction resolveStandardizedNameForImport(\n  type: \"plugin\" | \"preset\",\n  name: string,\n  dirname: string,\n) {\n  const parentUrl = pathToFileURL(\n    path.join(dirname, \"./babel-virtual-resolve-base.js\"),\n  ).href;\n\n  const it = resolveAlternativesHelper(type, name);\n  let res = it.next();\n  while (!res.done) {\n    res = it.next(tryImportMetaResolve(res.value, parentUrl));\n  }\n  return { loader: \"auto\" as const, filepath: fileURLToPath(res.value) };\n}\n\nfunction resolveStandardizedName(\n  type: \"plugin\" | \"preset\",\n  name: string,\n  dirname: string,\n  allowAsync: boolean,\n) {\n  if (!supportsESM || !allowAsync) {\n    return resolveStandardizedNameForRequire(type, name, dirname);\n  }\n\n  try {\n    const resolved = resolveStandardizedNameForImport(type, name, dirname);\n    // import-meta-resolve 4.0 does not throw if the module is not found.\n    if (!existsSync(resolved.filepath)) {\n      throw Object.assign(\n        new Error(`Could not resolve \"${name}\" in file ${dirname}.`),\n        { type: \"MODULE_NOT_FOUND\" },\n      );\n    }\n    return resolved;\n  } catch (e) {\n    try {\n      return resolveStandardizedNameForRequire(type, name, dirname);\n    } catch (e2) {\n      if (e.type === \"MODULE_NOT_FOUND\") throw e;\n      if (e2.type === \"MODULE_NOT_FOUND\") throw e2;\n      throw e;\n    }\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var LOADING_MODULES = new Set();\n}\nfunction* requireModule(\n  type: string,\n  loader: \"require\" | \"auto\",\n  name: string,\n): Handler<unknown> {\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!(yield* isAsync()) && LOADING_MODULES.has(name)) {\n      throw new Error(\n        `Reentrant ${type} detected trying to load \"${name}\". This module is not ignored ` +\n          \"and is trying to load itself while compiling itself, leading to a dependency cycle. \" +\n          'We recommend adding it to your \"ignore\" list in your babelrc, or to a .babelignore.',\n      );\n    }\n  }\n\n  try {\n    if (!process.env.BABEL_8_BREAKING) {\n      LOADING_MODULES.add(name);\n    }\n\n    if (process.env.BABEL_8_BREAKING) {\n      return yield* loadCodeDefault(\n        name,\n        loader,\n        `You appear to be using a native ECMAScript module ${type}, ` +\n          \"which is only supported when running Babel asynchronously \" +\n          \"or when using the Node.js `--experimental-require-module` flag.\",\n        `You appear to be using a ${type} that contains top-level await, ` +\n          \"which is only supported when running Babel asynchronously.\",\n      );\n    } else {\n      return yield* loadCodeDefault(\n        name,\n        loader,\n        `You appear to be using a native ECMAScript module ${type}, ` +\n          \"which is only supported when running Babel asynchronously \" +\n          \"or when using the Node.js `--experimental-require-module` flag.\",\n        `You appear to be using a ${type} that contains top-level await, ` +\n          \"which is only supported when running Babel asynchronously.\",\n        // For backward compatibility, we need to support malformed presets\n        // defined as separate named exports rather than a single default\n        // export.\n        // See packages/babel-core/test/fixtures/option-manager/presets/es2015_named.js\n        // @ts-ignore(Babel 7 vs Babel 8) This param has been removed\n        true,\n      );\n    }\n  } catch (err) {\n    err.message = `[BABEL]: ${err.message} (While processing: ${name})`;\n    throw err;\n  } finally {\n    if (!process.env.BABEL_8_BREAKING) {\n      LOADING_MODULES.delete(name);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAIA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAG,MAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AACA,SAAAI,KAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAM,kBAAA,GAAAL,OAAA;AAGA,SAAAM,IAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,GAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,MAAMQ,KAAK,GAAGC,OAASA,CAAC,CAAC,oCAAoC,CAAC;AAE9D,MAAMC,QAAQ,GAAG,UAAU;AAC3B,MAAMC,sBAAsB,GAAG,sCAAsC;AACrE,MAAMC,sBAAsB,GAAG,sCAAsC;AACrE,MAAMC,mBAAmB,GAAG,gCAAgC;AAC5D,MAAMC,mBAAmB,GAAG,gCAAgC;AAC5D,MAAMC,mBAAmB,GACvB,+DAA+D;AACjE,MAAMC,mBAAmB,GACvB,+DAA+D;AACjE,MAAMC,oBAAoB,GAAG,sBAAsB;AAE5C,MAAMC,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAGE,uBAAuB,CAACC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AAClE,MAAMC,aAAa,GAAAH,OAAA,CAAAG,aAAA,GAAGF,uBAAuB,CAACC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AAElE,UAAUE,UAAUA,CACzBC,IAAY,EACZC,OAAe,EACgC;EAC/C,MAAM;IAAEC,QAAQ;IAAEC;EAAO,CAAC,GAAGT,aAAa,CAACM,IAAI,EAAEC,OAAO,EAAE,OAAO,IAAAG,cAAO,EAAC,CAAC,CAAC;EAE3E,MAAMC,KAAK,GAAG,OAAOC,aAAa,CAAC,QAAQ,EAAEH,MAAM,EAAED,QAAQ,CAAC;EAC9DlB,KAAK,CAAC,2BAA2B,EAAEgB,IAAI,EAAEC,OAAO,CAAC;EAEjD,OAAO;IAAEC,QAAQ;IAAEG;EAAM,CAAC;AAC5B;AAEO,UAAUE,UAAUA,CACzBP,IAAY,EACZC,OAAe,EACgC;EAC/C,MAAM;IAAEC,QAAQ;IAAEC;EAAO,CAAC,GAAGL,aAAa,CAACE,IAAI,EAAEC,OAAO,EAAE,OAAO,IAAAG,cAAO,EAAC,CAAC,CAAC;EAE3E,MAAMC,KAAK,GAAG,OAAOC,aAAa,CAAC,QAAQ,EAAEH,MAAM,EAAED,QAAQ,CAAC;EAE9DlB,KAAK,CAAC,2BAA2B,EAAEgB,IAAI,EAAEC,OAAO,CAAC;EAEjD,OAAO;IAAEC,QAAQ;IAAEG;EAAM,CAAC;AAC5B;AAEA,SAASG,eAAeA,CAACC,IAAyB,EAAET,IAAY,EAAE;EAEhE,IAAIU,MAAGA,CAAC,CAACC,UAAU,CAACX,IAAI,CAAC,EAAE,OAAOA,IAAI;EAEtC,MAAMY,QAAQ,GAAGH,IAAI,KAAK,QAAQ;EAElC,OACET,IAAI,CAEDa,OAAO,CACND,QAAQ,GAAGxB,sBAAsB,GAAGD,sBAAsB,EAC1D,SAASsB,IAAI,GACf,CAAC,CAEAI,OAAO,CACND,QAAQ,GAAGtB,mBAAmB,GAAGD,mBAAmB,EACpD,KAAKoB,IAAI,GACX,CAAC,CAEAI,OAAO,CACND,QAAQ,GAAGpB,mBAAmB,GAAGD,mBAAmB,EACpD,WAAWkB,IAAI,GACjB,CAAC,CAEAI,OAAO,CAACpB,oBAAoB,EAAE,YAAYgB,IAAI,EAAE,CAAC,CAEjDI,OAAO,CAAC3B,QAAQ,EAAE,EAAE,CAAC;AAE5B;AAIA,UAAU4B,yBAAyBA,CACjCL,IAAyB,EACzBT,IAAY,EAC8B;EAC1C,MAAMe,gBAAgB,GAAGP,eAAe,CAACC,IAAI,EAAET,IAAI,CAAC;EACpD,MAAM;IAAEgB,KAAK;IAAEX;EAAM,CAAC,GAAG,MAAMU,gBAAgB;EAC/C,IAAI,CAACC,KAAK,EAAE,OAAOX,KAAK;EAGxB,IAAIW,KAAK,CAACC,IAAI,KAAK,kBAAkB,EAAE,MAAMD,KAAK;EAElD,IAAID,gBAAgB,KAAKf,IAAI,IAAI,CAAC,CAAC,MAAMA,IAAI,EAAEgB,KAAK,EAAE;IACpDA,KAAK,CAACE,OAAO,IAAI,+BAA+BlB,IAAI,kBAAkBA,IAAI,GAAG;EAC/E;EAEA,IAAI,CAAC,CAAC,MAAMQ,eAAe,CAACC,IAAI,EAAE,SAAS,GAAGT,IAAI,CAAC,EAAEgB,KAAK,EAAE;IAC1DA,KAAK,CAACE,OAAO,IAAI,4BAA4BlB,IAAI,IAAI;EACvD;EAEA,MAAMmB,YAAY,GAAGV,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ;EAC5D,IAAI,CAAC,CAAC,MAAMD,eAAe,CAACW,YAAY,EAAEnB,IAAI,CAAC,EAAEgB,KAAK,EAAE;IACtDA,KAAK,CAACE,OAAO,IAAI,mCAAmCC,YAAY,SAASV,IAAI,GAAG;EAClF;EAEA,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrB,MAAMW,aAAa,GAAGL,gBAAgB,CAACF,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC;IAC3E,IAAIO,aAAa,KAAKL,gBAAgB,IAAI,CAAC,CAAC,MAAMK,aAAa,EAAEJ,KAAK,EAAE;MACtEA,KAAK,CAACE,OAAO,IAAI,qBAAqBE,aAAa,IAAI;IACzD;EACF;EAEAJ,KAAK,CAACE,OAAO,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;EAEC,MAAMF,KAAK;AACb;AAEA,SAASK,iBAAiBA,CACxBC,EAAU,EACVrB,OAA2B,EACX;EAChB,IAAI;IACF,IAAIA,OAAO,EAAE;MACX,OAAO;QAAEe,KAAK,EAAE,IAAI;QAAEX,KAAK,EAAE,GAAAkB,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,WAAAnD,OAAA,CAAAoD,OAAA,IAAAC,CAAA;UAAAC,KAAA,GAAAC,CAAA;QAAA,GAAAC,CAAA,GAAAxD,OAAA;UAAA,IAAAyD,CAAA,GAAAD,CAAA,CAAAE,SAAA,CAAAL,CAAA,EAAAG,CAAA,CAAAG,gBAAA,CAAAJ,CAAA,EAAAK,MAAA,CAAAL,CAAA;UAAA,IAAAE,CAAA,SAAAA,CAAA;UAAAA,CAAA,OAAAI,KAAA,2BAAAR,CAAA;UAAAI,CAAA,CAAAjB,IAAA;UAAA,MAAAiB,CAAA;QAAA,GAAgBZ,EAAE,EAAE;UAAES,KAAK,EAAE,CAAC9B,OAAO;QAAE,CAAC;MAAE,CAAC;IAC1E,CAAC,MAAM;MACL,OAAO;QAAEe,KAAK,EAAE,IAAI;QAAEX,KAAK,EAAE5B,OAAO,CAACoD,OAAO,CAACP,EAAE;MAAE,CAAC;IACpD;EACF,CAAC,CAAC,OAAON,KAAK,EAAE;IACd,OAAO;MAAEA,KAAK;MAAEX,KAAK,EAAE;IAAK,CAAC;EAC/B;AACF;AAEA,SAASkC,oBAAoBA,CAC3BjB,EAA2C,EAC3CkB,OAAgD,EAChC;EAChB,IAAI;IACF,OAAO;MAAExB,KAAK,EAAE,IAAI;MAAEX,KAAK,EAAE,IAAAoC,0BAAiB,EAACnB,EAAE,EAAEkB,OAAO;IAAE,CAAC;EAC/D,CAAC,CAAC,OAAOxB,KAAK,EAAE;IACd,OAAO;MAAEA,KAAK;MAAEX,KAAK,EAAE;IAAK,CAAC;EAC/B;AACF;AAEA,SAASqC,iCAAiCA,CACxCjC,IAAyB,EACzBT,IAAY,EACZC,OAAe,EACf;EACA,MAAM0C,EAAE,GAAG7B,yBAAyB,CAACL,IAAI,EAAET,IAAI,CAAC;EAChD,IAAI4C,GAAG,GAAGD,EAAE,CAACE,IAAI,CAAC,CAAC;EACnB,OAAO,CAACD,GAAG,CAACE,IAAI,EAAE;IAChBF,GAAG,GAAGD,EAAE,CAACE,IAAI,CAACxB,iBAAiB,CAACuB,GAAG,CAACvC,KAAK,EAAEJ,OAAO,CAAC,CAAC;EACtD;EACA,OAAO;IAAEE,MAAM,EAAE,SAAkB;IAAED,QAAQ,EAAE0C,GAAG,CAACvC;EAAM,CAAC;AAC5D;AACA,SAAS0C,gCAAgCA,CACvCtC,IAAyB,EACzBT,IAAY,EACZC,OAAe,EACf;EACA,MAAM+C,SAAS,GAAG,IAAAC,oBAAa,EAC7BvC,MAAGA,CAAC,CAACwC,IAAI,CAACjD,OAAO,EAAE,iCAAiC,CACtD,CAAC,CAACkD,IAAI;EAEN,MAAMR,EAAE,GAAG7B,yBAAyB,CAACL,IAAI,EAAET,IAAI,CAAC;EAChD,IAAI4C,GAAG,GAAGD,EAAE,CAACE,IAAI,CAAC,CAAC;EACnB,OAAO,CAACD,GAAG,CAACE,IAAI,EAAE;IAChBF,GAAG,GAAGD,EAAE,CAACE,IAAI,CAACN,oBAAoB,CAACK,GAAG,CAACvC,KAAK,EAAE2C,SAAS,CAAC,CAAC;EAC3D;EACA,OAAO;IAAE7C,MAAM,EAAE,MAAe;IAAED,QAAQ,EAAE,IAAAkD,oBAAa,EAACR,GAAG,CAACvC,KAAK;EAAE,CAAC;AACxE;AAEA,SAAST,uBAAuBA,CAC9Ba,IAAyB,EACzBT,IAAY,EACZC,OAAe,EACfoD,UAAmB,EACnB;EACA,IAAI,CAACC,wBAAW,IAAI,CAACD,UAAU,EAAE;IAC/B,OAAOX,iCAAiC,CAACjC,IAAI,EAAET,IAAI,EAAEC,OAAO,CAAC;EAC/D;EAEA,IAAI;IACF,MAAMsD,QAAQ,GAAGR,gCAAgC,CAACtC,IAAI,EAAET,IAAI,EAAEC,OAAO,CAAC;IAEtE,IAAI,CAAC,IAAAuD,gBAAU,EAACD,QAAQ,CAACrD,QAAQ,CAAC,EAAE;MAClC,MAAMuD,MAAM,CAACC,MAAM,CACjB,IAAIpB,KAAK,CAAC,sBAAsBtC,IAAI,aAAaC,OAAO,GAAG,CAAC,EAC5D;QAAEQ,IAAI,EAAE;MAAmB,CAC7B,CAAC;IACH;IACA,OAAO8C,QAAQ;EACjB,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,IAAI;MACF,OAAOjB,iCAAiC,CAACjC,IAAI,EAAET,IAAI,EAAEC,OAAO,CAAC;IAC/D,CAAC,CAAC,OAAO2D,EAAE,EAAE;MACX,IAAID,CAAC,CAAClD,IAAI,KAAK,kBAAkB,EAAE,MAAMkD,CAAC;MAC1C,IAAIC,EAAE,CAACnD,IAAI,KAAK,kBAAkB,EAAE,MAAMmD,EAAE;MAC5C,MAAMD,CAAC;IACT;EACF;AACF;AAEmC;EAEjC,IAAIE,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC;AACA,UAAUxD,aAAaA,CACrBG,IAAY,EACZN,MAA0B,EAC1BH,IAAY,EACM;EACiB;IACjC,IAAI,EAAE,OAAO,IAAAI,cAAO,EAAC,CAAC,CAAC,IAAIyD,eAAe,CAACE,GAAG,CAAC/D,IAAI,CAAC,EAAE;MACpD,MAAM,IAAIsC,KAAK,CACb,aAAa7B,IAAI,6BAA6BT,IAAI,gCAAgC,GAChF,sFAAsF,GACtF,qFACJ,CAAC;IACH;EACF;EAEA,IAAI;IACiC;MACjC6D,eAAe,CAACG,GAAG,CAAChE,IAAI,CAAC;IAC3B;IAYO;MACL,OAAO,OAAO,IAAAiE,oBAAe,EAC3BjE,IAAI,EACJG,MAAM,EACN,qDAAqDM,IAAI,IAAI,GAC3D,4DAA4D,GAC5D,iEAAiE,EACnE,4BAA4BA,IAAI,kCAAkC,GAChE,4DAA4D,EAM9D,IACF,CAAC;IACH;EACF,CAAC,CAAC,OAAOyD,GAAG,EAAE;IACZA,GAAG,CAAChD,OAAO,GAAG,YAAYgD,GAAG,CAAChD,OAAO,uBAAuBlB,IAAI,GAAG;IACnE,MAAMkE,GAAG;EACX,CAAC,SAAS;IAC2B;MACjCL,eAAe,CAACM,MAAM,CAACnE,IAAI,CAAC;IAC9B;EACF;AACF;AAAC", "ignoreList": []}