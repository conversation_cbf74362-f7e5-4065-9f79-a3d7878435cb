{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "loose", "_api$assumption", "assertVersion", "noDocumentAll", "assumption", "name", "manipulateOptions", "_", "parser", "plugins", "push", "visitor", "LogicalExpression", "path", "node", "scope", "operator", "ref", "assignment", "isStatic", "left", "t", "cloneNode", "isPattern", "replaceWith", "template", "statement", "ast", "generateUidIdentifierBasedOnNode", "id", "assignmentExpression", "conditionalExpression", "binaryExpression", "nullLiteral", "logicalExpression", "buildUndefinedNode", "right"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, template } from \"@babel/core\";\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, { loose = false }: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const noDocumentAll = api.assumption(\"noDocumentAll\") ?? loose;\n\n  return {\n    name: \"transform-nullish-coalescing-operator\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"nullishCoalescingOperator\"),\n\n    visitor: {\n      LogicalExpression(path) {\n        const { node, scope } = path;\n        if (node.operator !== \"??\") {\n          return;\n        }\n\n        let ref;\n        let assignment;\n        // skip creating extra reference when `left` is static\n        if (scope.isStatic(node.left)) {\n          ref = node.left;\n          assignment = t.cloneNode(node.left);\n        } else if (scope.path.isPattern()) {\n          // Replace `function (a, x = a.b ?? c) {}` to `function (a, x = (() => a.b ?? c)() ){}`\n          // so the temporary variable can be injected in correct scope\n          path.replaceWith(template.statement.ast`(() => ${path.node})()`);\n          // The injected nullish expression will be queued and eventually transformed when visited\n          return;\n        } else {\n          ref = scope.generateUidIdentifierBasedOnNode(node.left);\n          scope.push({ id: t.cloneNode(ref) });\n          assignment = t.assignmentExpression(\"=\", ref, node.left);\n        }\n\n        path.replaceWith(\n          t.conditionalExpression(\n            // We cannot use `!= null` in spec mode because\n            // `document.all == null` and `document.all` is not \"nullish\".\n            noDocumentAll\n              ? t.binaryExpression(\"!=\", assignment, t.nullLiteral())\n              : t.logicalExpression(\n                  \"&&\",\n                  t.binaryExpression(\"!==\", assignment, t.nullLiteral()),\n                  t.binaryExpression(\n                    \"!==\",\n                    t.cloneNode(ref),\n                    scope.buildUndefinedNode(),\n                  ),\n                ),\n            t.cloneNode(ref),\n            node.right,\n          ),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAmD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMpC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAE;EAAEC,KAAK,GAAG;AAAe,CAAC,KAAK;EAAA,IAAAC,eAAA;EAC1DF,GAAG,CAACG,aAAa,uCAAoB,CAAC;EACtC,MAAMC,aAAa,IAAAF,eAAA,GAAGF,GAAG,CAACK,UAAU,CAAC,eAAe,CAAC,YAAAH,eAAA,GAAID,KAAK;EAE9D,OAAO;IACLK,IAAI,EAAE,uCAAuC;IAC7CC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,2BAA2B,CAAC;IAEnEC,OAAO,EAAE;MACPC,iBAAiBA,CAACC,IAAI,EAAE;QACtB,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGF,IAAI;QAC5B,IAAIC,IAAI,CAACE,QAAQ,KAAK,IAAI,EAAE;UAC1B;QACF;QAEA,IAAIC,GAAG;QACP,IAAIC,UAAU;QAEd,IAAIH,KAAK,CAACI,QAAQ,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;UAC7BH,GAAG,GAAGH,IAAI,CAACM,IAAI;UACfF,UAAU,GAAGG,WAAC,CAACC,SAAS,CAACR,IAAI,CAACM,IAAI,CAAC;QACrC,CAAC,MAAM,IAAIL,KAAK,CAACF,IAAI,CAACU,SAAS,CAAC,CAAC,EAAE;UAGjCV,IAAI,CAACW,WAAW,CAACC,cAAQ,CAACC,SAAS,CAACC,GAAG,UAAUd,IAAI,CAACC,IAAI,KAAK,CAAC;UAEhE;QACF,CAAC,MAAM;UACLG,GAAG,GAAGF,KAAK,CAACa,gCAAgC,CAACd,IAAI,CAACM,IAAI,CAAC;UACvDL,KAAK,CAACL,IAAI,CAAC;YAAEmB,EAAE,EAAER,WAAC,CAACC,SAAS,CAACL,GAAG;UAAE,CAAC,CAAC;UACpCC,UAAU,GAAGG,WAAC,CAACS,oBAAoB,CAAC,GAAG,EAAEb,GAAG,EAAEH,IAAI,CAACM,IAAI,CAAC;QAC1D;QAEAP,IAAI,CAACW,WAAW,CACdH,WAAC,CAACU,qBAAqB,CAGrB5B,aAAa,GACTkB,WAAC,CAACW,gBAAgB,CAAC,IAAI,EAAEd,UAAU,EAAEG,WAAC,CAACY,WAAW,CAAC,CAAC,CAAC,GACrDZ,WAAC,CAACa,iBAAiB,CACjB,IAAI,EACJb,WAAC,CAACW,gBAAgB,CAAC,KAAK,EAAEd,UAAU,EAAEG,WAAC,CAACY,WAAW,CAAC,CAAC,CAAC,EACtDZ,WAAC,CAACW,gBAAgB,CAChB,KAAK,EACLX,WAAC,CAACC,SAAS,CAACL,GAAG,CAAC,EAChBF,KAAK,CAACoB,kBAAkB,CAAC,CAC3B,CACF,CAAC,EACLd,WAAC,CAACC,SAAS,CAACL,GAAG,CAAC,EAChBH,IAAI,CAACsB,KACP,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}