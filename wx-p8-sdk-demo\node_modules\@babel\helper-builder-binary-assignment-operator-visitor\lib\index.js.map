{"version": 3, "names": ["_t", "require", "_explodeAssignableExpression", "assignmentExpression", "sequenceExpression", "_default", "opts", "build", "operator", "visitor", "AssignmentExpression", "path", "node", "scope", "nodes", "exploded", "explode", "left", "push", "ref", "uid", "right", "replaceWith", "BinaryExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { assignmentExpression, sequenceExpression } from \"@babel/types\";\nimport type { Visitor } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nimport explode from \"./explode-assignable-expression.ts\";\n\nexport default function (opts: {\n  build: (\n    left: t.Expression | t.PrivateName | t.Super,\n    right: t.Expression,\n  ) => t.Expression;\n  operator: t.BinaryExpression[\"operator\"];\n}) {\n  const { build, operator } = opts;\n\n  const visitor: Visitor = {\n    AssignmentExpression(path) {\n      const { node, scope } = path;\n      if (node.operator !== operator + \"=\") return;\n\n      const nodes: t.AssignmentExpression[] = [];\n      // @ts-expect-error Fixme: node.left can be a TSAsExpression\n      const exploded = explode(node.left, nodes, scope);\n      nodes.push(\n        assignmentExpression(\n          \"=\",\n          exploded.ref,\n          build(exploded.uid, node.right),\n        ),\n      );\n      path.replaceWith(sequenceExpression(nodes));\n    },\n\n    BinaryExpression(path) {\n      const { node } = path;\n      if (node.operator === operator) {\n        path.replaceWith(build(node.left, node.right));\n      }\n    },\n  };\n  return visitor;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAIA,IAAAC,4BAAA,GAAAD,OAAA;AAAyD;EAJhDE,oBAAoB;EAAEC;AAAkB,IAAAJ,EAAA;AAMlC,SAAAK,SAAUC,IAMxB,EAAE;EACD,MAAM;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGF,IAAI;EAEhC,MAAMG,OAAgB,GAAG;IACvBC,oBAAoBA,CAACC,IAAI,EAAE;MACzB,MAAM;QAAEC,IAAI;QAAEC;MAAM,CAAC,GAAGF,IAAI;MAC5B,IAAIC,IAAI,CAACJ,QAAQ,KAAKA,QAAQ,GAAG,GAAG,EAAE;MAEtC,MAAMM,KAA+B,GAAG,EAAE;MAE1C,MAAMC,QAAQ,GAAG,IAAAC,oCAAO,EAACJ,IAAI,CAACK,IAAI,EAAEH,KAAK,EAAED,KAAK,CAAC;MACjDC,KAAK,CAACI,IAAI,CACRf,oBAAoB,CAClB,GAAG,EACHY,QAAQ,CAACI,GAAG,EACZZ,KAAK,CAACQ,QAAQ,CAACK,GAAG,EAAER,IAAI,CAACS,KAAK,CAChC,CACF,CAAC;MACDV,IAAI,CAACW,WAAW,CAAClB,kBAAkB,CAACU,KAAK,CAAC,CAAC;IAC7C,CAAC;IAEDS,gBAAgBA,CAACZ,IAAI,EAAE;MACrB,MAAM;QAAEC;MAAK,CAAC,GAAGD,IAAI;MACrB,IAAIC,IAAI,CAACJ,QAAQ,KAAKA,QAAQ,EAAE;QAC9BG,IAAI,CAACW,WAAW,CAACf,KAAK,CAACK,IAAI,CAACK,IAAI,EAAEL,IAAI,CAACS,KAAK,CAAC,CAAC;MAChD;IACF;EACF,CAAC;EACD,OAAOZ,OAAO;AAChB", "ignoreList": []}