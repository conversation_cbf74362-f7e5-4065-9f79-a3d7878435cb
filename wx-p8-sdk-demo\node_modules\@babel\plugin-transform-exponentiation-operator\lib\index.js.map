{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperBuilderBinaryAssignmentOperatorVisitor", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "build", "operator", "left", "right", "t", "callExpression", "memberExpression", "identifier"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport build from \"@babel/helper-builder-binary-assignment-operator-visitor\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-exponentiation-operator\",\n\n    visitor: build({\n      operator: \"**\",\n\n      build(left, right) {\n        return t.callExpression(\n          t.memberExpression(t.identifier(\"Math\"), t.identifier(\"pow\")),\n          [\n            // left can be PrivateName only if operator is `\"in\"`\n            left as t.Expression,\n            right,\n          ],\n        );\n      },\n    }),\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,6CAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAAyC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,mCAAmC;IAEzCC,OAAO,EAAE,IAAAC,qDAAK,EAAC;MACbC,QAAQ,EAAE,IAAI;MAEdD,KAAKA,CAACE,IAAI,EAAEC,KAAK,EAAE;QACjB,OAAOC,WAAC,CAACC,cAAc,CACrBD,WAAC,CAACE,gBAAgB,CAACF,WAAC,CAACG,UAAU,CAAC,MAAM,CAAC,EAAEH,WAAC,CAACG,UAAU,CAAC,KAAK,CAAC,CAAC,EAC7D,CAEEL,IAAI,EACJC,KAAK,CAET,CAAC;MACH;IACF,CAAC;EACH,CAAC;AACH,CAAC,CAAC", "ignoreList": []}