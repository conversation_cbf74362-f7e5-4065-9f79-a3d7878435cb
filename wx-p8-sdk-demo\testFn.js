// function testPromise() {
//   return new Promise((resolve) => {
//     console.log('Promise 开始');
//     resolve('成功');
//     console.log('resolve 后还会执行这里');
//   }).finally(() => {
//     console.log('finally 执行了');
//   });
// }

// testPromise().then(result => {
//   console.log('then:', result);
// });

// 添加一个debug测试函数
let p8QuickApp = false

function testHttpRequestRetry() {
  let attemptCount = 0;
  const originalWxRequest = wx.request;

  // 模拟前两次请求失败，第三次成功
  wx.request = function (options) {
    attemptCount++;
    console.log(`Debug: 模拟请求尝试 ${attemptCount}`);

    setTimeout(() => {
      if (attemptCount <= 2) {
        // 前两次失败
        console.log(`Debug: 模拟第 ${attemptCount} 次请求失败`);
        options.fail({
          errMsg: `request:fail 模拟失败 ${attemptCount}`,
          statusCode: 500
        });
      } else {
        // 第三次成功
        console.log(`Debug: 模拟第 ${attemptCount} 次请求成功`);
        options.success({
          data: {
            message: "请求成功"
          },
          statusCode: 200
        });
      }
    }, 100);
  };

  // 测试重试机制
  console.log("开始测试 HttpRequest 重试机制...");
  HttpRequest(
    "https://test-api.example.com/test",
    "get",
    null,
    function (response) {
      console.log("最终响应:", response);
      // 恢复原始的 wx.request
      wx.request = originalWxRequest;
      console.log("测试完成，已恢复原始 wx.request");
    },
    null
  );
}

// 优化后的 HttpRequest 函数，添加更详细的日志
function HttpRequest(url, t = "get", i = null, r = null, f = null, retryCount = 0) {
  let retryTimer = null;

  console.log(`HttpRequest: 开始请求 ${url}, 方法: ${t}, 重试次数: ${retryCount}`);

  if (p8QuickApp) {
    XmlHttpRequestLog(url, t, i, r, f);
  } else {
    wx.request({
      url: url,
      method: t,
      data: i,
      header: {
        "content-type": "application/x-www-form-urlencoded",
      },
      success: function (e) {
        console.log(`HttpRequest: 请求成功 ${url}, 重试次数: ${retryCount}`);
        if (retryTimer) {
          clearTimeout(retryTimer);
          retryTimer = null;
        }
        r(e);
      },
      fail: function (e) {
        console.log(`HttpRequest: 请求失败 ${url}, 重试次数: ${retryCount}, 错误:`, e);

        if (retryCount < 3) {
          console.warn(`HttpRequest 请求失败, 将在1秒后重试... (尝试 ${retryCount + 1}/3)`);
          if (retryTimer) {
            clearTimeout(retryTimer);
          }
          retryTimer = setTimeout(() => {
            console.log(`HttpRequest: 开始第 ${retryCount + 1} 次重试`);
            HttpRequest(url, t, i, r, f, retryCount + 1);
          }, 1000);
        } else {
          console.warn("HttpRequest 请求失败,超过3次,不再重试");
          if (retryTimer) {
            clearTimeout(retryTimer);
            retryTimer = null;
          }
          r(e);
        }
      }
    });
  }
}

testHttpRequestRetry()