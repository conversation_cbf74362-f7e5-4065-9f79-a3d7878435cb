{"version": 3, "names": ["_gensync", "data", "require", "ChainFormatter", "exports", "Programmatic", "Config", "<PERSON><PERSON><PERSON>", "title", "type", "callerName", "filepath", "loc", "index", "envName", "optionsAndDescriptors", "opt", "content", "Object", "assign", "options", "overrides", "env", "pluginDescriptors", "plugins", "length", "map", "d", "descriptorToConfig", "presetDescriptors", "presets", "JSON", "stringify", "undefined", "_d$file", "name", "file", "request", "value", "toString", "slice", "ConfigPrinter", "constructor", "_stack", "configure", "enabled", "push", "format", "config", "output", "configs", "gens<PERSON>", "all", "s", "join"], "sources": ["../../src/config/printer.ts"], "sourcesContent": ["import gensync from \"gensync\";\n\nimport type { <PERSON><PERSON> } from \"gensync\";\n\nimport type {\n  OptionsAndDescriptors,\n  UnloadedDescriptor,\n} from \"./config-descriptors.ts\";\n\n// todo: Use flow enums when @babel/transform-flow-types supports it\nexport const ChainFormatter = {\n  Programmatic: 0,\n  Config: 1,\n};\n\ntype PrintableConfig = {\n  content: OptionsAndDescriptors;\n  type: (typeof ChainFormatter)[keyof typeof ChainFormatter];\n  callerName: string | undefined | null;\n  filepath: string | undefined | null;\n  index: number | undefined | null;\n  envName: string | undefined | null;\n};\n\nconst Formatter = {\n  title(\n    type: (typeof ChainFormatter)[keyof typeof ChainFormatter],\n    callerName?: string | null,\n    filepath?: string | null,\n  ): string {\n    let title = \"\";\n    if (type === ChainFormatter.Programmatic) {\n      title = \"programmatic options\";\n      if (callerName) {\n        title += \" from \" + callerName;\n      }\n    } else {\n      title = \"config \" + filepath;\n    }\n    return title;\n  },\n  loc(index?: number | null, envName?: string | null): string {\n    let loc = \"\";\n    if (index != null) {\n      loc += `.overrides[${index}]`;\n    }\n    if (envName != null) {\n      loc += `.env[\"${envName}\"]`;\n    }\n    return loc;\n  },\n\n  *optionsAndDescriptors(opt: OptionsAndDescriptors) {\n    const content = { ...opt.options };\n    // overrides and env will be printed as separated config items\n    delete content.overrides;\n    delete content.env;\n    // resolve to descriptors\n    const pluginDescriptors = [...(yield* opt.plugins())];\n    if (pluginDescriptors.length) {\n      content.plugins = pluginDescriptors.map(d => descriptorToConfig(d));\n    }\n    const presetDescriptors = [...(yield* opt.presets())];\n    if (presetDescriptors.length) {\n      content.presets = [...presetDescriptors].map(d => descriptorToConfig(d));\n    }\n    return JSON.stringify(content, undefined, 2);\n  },\n};\n\nfunction descriptorToConfig<API>(\n  d: UnloadedDescriptor<API>,\n): object | string | [string, unknown] | [string, unknown, string] {\n  let name: object | string = d.file?.request;\n  if (name == null) {\n    if (typeof d.value === \"object\") {\n      name = d.value;\n    } else if (typeof d.value === \"function\") {\n      // If the unloaded descriptor is a function, i.e. `plugins: [ require(\"my-plugin\") ]`,\n      // we print the first 50 characters of the function source code and hopefully we can see\n      // `name: 'my-plugin'` in the source\n      name = `[Function: ${d.value.toString().slice(0, 50)} ... ]`;\n    }\n  }\n  if (name == null) {\n    name = \"[Unknown]\";\n  }\n  if (d.options === undefined) {\n    return name;\n  } else if (d.name == null) {\n    return [name, d.options];\n  } else {\n    return [name, d.options, d.name];\n  }\n}\n\nexport class ConfigPrinter {\n  _stack: Array<PrintableConfig> = [];\n  configure(\n    enabled: boolean,\n    type: (typeof ChainFormatter)[keyof typeof ChainFormatter],\n    {\n      callerName,\n      filepath,\n    }: {\n      callerName?: string;\n      filepath?: string;\n    },\n  ) {\n    if (!enabled) return () => {};\n    return (\n      content: OptionsAndDescriptors,\n      index?: number | null,\n      envName?: string | null,\n    ) => {\n      this._stack.push({\n        type,\n        callerName,\n        filepath,\n        content,\n        index,\n        envName,\n      });\n    };\n  }\n  static *format(config: PrintableConfig): Handler<string> {\n    let title = Formatter.title(\n      config.type,\n      config.callerName,\n      config.filepath,\n    );\n    const loc = Formatter.loc(config.index, config.envName);\n    if (loc) title += ` ${loc}`;\n    const content = yield* Formatter.optionsAndDescriptors(config.content);\n    return `${title}\\n${content}`;\n  }\n\n  *output(): Handler<string> {\n    if (this._stack.length === 0) return \"\";\n    const configs = yield* gensync.all(\n      this._stack.map(s => ConfigPrinter.format(s)),\n    );\n    return configs.join(\"\\n\\n\");\n  }\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAUO,MAAME,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG;EAC5BE,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE;AACV,CAAC;AAWD,MAAMC,SAAS,GAAG;EAChBC,KAAKA,CACHC,IAA0D,EAC1DC,UAA0B,EAC1BC,QAAwB,EAChB;IACR,IAAIH,KAAK,GAAG,EAAE;IACd,IAAIC,IAAI,KAAKN,cAAc,CAACE,YAAY,EAAE;MACxCG,KAAK,GAAG,sBAAsB;MAC9B,IAAIE,UAAU,EAAE;QACdF,KAAK,IAAI,QAAQ,GAAGE,UAAU;MAChC;IACF,CAAC,MAAM;MACLF,KAAK,GAAG,SAAS,GAAGG,QAAQ;IAC9B;IACA,OAAOH,KAAK;EACd,CAAC;EACDI,GAAGA,CAACC,KAAqB,EAAEC,OAAuB,EAAU;IAC1D,IAAIF,GAAG,GAAG,EAAE;IACZ,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjBD,GAAG,IAAI,cAAcC,KAAK,GAAG;IAC/B;IACA,IAAIC,OAAO,IAAI,IAAI,EAAE;MACnBF,GAAG,IAAI,SAASE,OAAO,IAAI;IAC7B;IACA,OAAOF,GAAG;EACZ,CAAC;EAED,CAACG,qBAAqBA,CAACC,GAA0B,EAAE;IACjD,MAAMC,OAAO,GAAAC,MAAA,CAAAC,MAAA,KAAQH,GAAG,CAACI,OAAO,CAAE;IAElC,OAAOH,OAAO,CAACI,SAAS;IACxB,OAAOJ,OAAO,CAACK,GAAG;IAElB,MAAMC,iBAAiB,GAAG,CAAC,IAAI,OAAOP,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;IACrD,IAAID,iBAAiB,CAACE,MAAM,EAAE;MAC5BR,OAAO,CAACO,OAAO,GAAGD,iBAAiB,CAACG,GAAG,CAACC,CAAC,IAAIC,kBAAkB,CAACD,CAAC,CAAC,CAAC;IACrE;IACA,MAAME,iBAAiB,GAAG,CAAC,IAAI,OAAOb,GAAG,CAACc,OAAO,CAAC,CAAC,CAAC,CAAC;IACrD,IAAID,iBAAiB,CAACJ,MAAM,EAAE;MAC5BR,OAAO,CAACa,OAAO,GAAG,CAAC,GAAGD,iBAAiB,CAAC,CAACH,GAAG,CAACC,CAAC,IAAIC,kBAAkB,CAACD,CAAC,CAAC,CAAC;IAC1E;IACA,OAAOI,IAAI,CAACC,SAAS,CAACf,OAAO,EAAEgB,SAAS,EAAE,CAAC,CAAC;EAC9C;AACF,CAAC;AAED,SAASL,kBAAkBA,CACzBD,CAA0B,EACuC;EAAA,IAAAO,OAAA;EACjE,IAAIC,IAAqB,IAAAD,OAAA,GAAGP,CAAC,CAACS,IAAI,qBAANF,OAAA,CAAQG,OAAO;EAC3C,IAAIF,IAAI,IAAI,IAAI,EAAE;IAChB,IAAI,OAAOR,CAAC,CAACW,KAAK,KAAK,QAAQ,EAAE;MAC/BH,IAAI,GAAGR,CAAC,CAACW,KAAK;IAChB,CAAC,MAAM,IAAI,OAAOX,CAAC,CAACW,KAAK,KAAK,UAAU,EAAE;MAIxCH,IAAI,GAAG,cAAcR,CAAC,CAACW,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ;IAC9D;EACF;EACA,IAAIL,IAAI,IAAI,IAAI,EAAE;IAChBA,IAAI,GAAG,WAAW;EACpB;EACA,IAAIR,CAAC,CAACP,OAAO,KAAKa,SAAS,EAAE;IAC3B,OAAOE,IAAI;EACb,CAAC,MAAM,IAAIR,CAAC,CAACQ,IAAI,IAAI,IAAI,EAAE;IACzB,OAAO,CAACA,IAAI,EAAER,CAAC,CAACP,OAAO,CAAC;EAC1B,CAAC,MAAM;IACL,OAAO,CAACe,IAAI,EAAER,CAAC,CAACP,OAAO,EAAEO,CAAC,CAACQ,IAAI,CAAC;EAClC;AACF;AAEO,MAAMM,aAAa,CAAC;EAAAC,YAAA;IAAA,KACzBC,MAAM,GAA2B,EAAE;EAAA;EACnCC,SAASA,CACPC,OAAgB,EAChBpC,IAA0D,EAC1D;IACEC,UAAU;IACVC;EAIF,CAAC,EACD;IACA,IAAI,CAACkC,OAAO,EAAE,OAAO,MAAM,CAAC,CAAC;IAC7B,OAAO,CACL5B,OAA8B,EAC9BJ,KAAqB,EACrBC,OAAuB,KACpB;MACH,IAAI,CAAC6B,MAAM,CAACG,IAAI,CAAC;QACfrC,IAAI;QACJC,UAAU;QACVC,QAAQ;QACRM,OAAO;QACPJ,KAAK;QACLC;MACF,CAAC,CAAC;IACJ,CAAC;EACH;EACA,QAAQiC,MAAMA,CAACC,MAAuB,EAAmB;IACvD,IAAIxC,KAAK,GAAGD,SAAS,CAACC,KAAK,CACzBwC,MAAM,CAACvC,IAAI,EACXuC,MAAM,CAACtC,UAAU,EACjBsC,MAAM,CAACrC,QACT,CAAC;IACD,MAAMC,GAAG,GAAGL,SAAS,CAACK,GAAG,CAACoC,MAAM,CAACnC,KAAK,EAAEmC,MAAM,CAAClC,OAAO,CAAC;IACvD,IAAIF,GAAG,EAAEJ,KAAK,IAAI,IAAII,GAAG,EAAE;IAC3B,MAAMK,OAAO,GAAG,OAAOV,SAAS,CAACQ,qBAAqB,CAACiC,MAAM,CAAC/B,OAAO,CAAC;IACtE,OAAO,GAAGT,KAAK,KAAKS,OAAO,EAAE;EAC/B;EAEA,CAACgC,MAAMA,CAAA,EAAoB;IACzB,IAAI,IAAI,CAACN,MAAM,CAAClB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACvC,MAAMyB,OAAO,GAAG,OAAOC,SAAMA,CAAC,CAACC,GAAG,CAChC,IAAI,CAACT,MAAM,CAACjB,GAAG,CAAC2B,CAAC,IAAIZ,aAAa,CAACM,MAAM,CAACM,CAAC,CAAC,CAC9C,CAAC;IACD,OAAOH,OAAO,CAACI,IAAI,CAAC,MAAM,CAAC;EAC7B;AACF;AAAClD,OAAA,CAAAqC,aAAA,GAAAA,aAAA;AAAA", "ignoreList": []}