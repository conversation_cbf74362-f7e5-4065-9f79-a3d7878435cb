using System;
using GameFramework;
using GameProject.UI.Manager;
#if WECHATMINIGAME
using Framework.Event;
using GameUtility;
using WeChatWASM;

namespace Framework.System
{
    //目前全部使用P8SDK的广告单元
    public class WeixinAdvSystem : AbstractSystem, IAdvSystem
    {
        private WXRewardedVideoAd _rewardedVideoAd;
        private bool _isLoaded;
        private Action<bool, string> _callback;
        private ConsumeType _consumeType;
        private IReportSystem _reportSystem;
        private const string AdUnitId = "adunit-919050a217d7e81d";

        protected override void OnInit()
        {
            _reportSystem = this.GetSystem<IReportSystem>();
            InitializeRewardedVideoAd();
        }

        private void InitializeRewardedVideoAd()
        {
            _rewardedVideoAd = WXBase.CreateRewardedVideoAd(new WXCreateRewardedVideoAdParam { adUnitId = AdUnitId });

            _rewardedVideoAd.OnLoad(_ => {
                GameLogger.Log("广告加载成功");
                _isLoaded = true;
            });

            _rewardedVideoAd.OnError(err => {
                GameLogger.Error($"广告加载失败: {err.errMsg}");
                _isLoaded = false;
                _callback?.Invoke(false, "广告拉取失败");
                _callback = null;
                UIManager.Instance.HideUIMask();
                _reportSystem.ReportAdvEvent(_consumeType, AdvEvent.LoadVideoFail);
                TypeEventSystem.Global.Send<OnAdvCompleteEvent>();
            });

            _rewardedVideoAd.OnClose(res => {
                if (res.isEnded)
                {
                    _callback?.Invoke(true, "广告播放完成，奖励已发放");
                    GameLogger.Log("广告播放完成，奖励已发放");
                    _reportSystem.ReportAdvEvent(_consumeType, AdvEvent.CompleteVideoAdv);
                    TypeEventSystem.Global.Send<OnAdvCompleteEvent>();
                }
                else
                {
                    _callback?.Invoke(false, "");
                    GameLogger.Log("广告未完整播放");
                    _reportSystem.ReportAdvEvent(_consumeType, AdvEvent.QuiteVideoAdv);
                    TypeEventSystem.Global.Send<OnAdvCompleteEvent>();
                }
                _callback = null;
            });
        }

        public void ShowRewardedVideoAdv(Action<bool, string> callback, ConsumeType consumeType = ConsumeType.None)
        {
            if (!_isLoaded)
            {
                GameLogger.Log("广告未加载，尝试重新加载");
                _rewardedVideoAd.Load();
                callback?.Invoke(false, "广告未加载，尝试重新加载");
                TypeEventSystem.Global.Send<OnAdvCompleteEvent>();
                return;
            }

            _callback = callback;
            _consumeType = consumeType;
            
            UIManager.Instance.ShowUIMask("广告拉取中，请稍候...");
            _rewardedVideoAd.Show(
                _ =>
                {
                    _reportSystem.ReportAdvEvent(_consumeType, AdvEvent.ShowVideoAdv);
                    GameLogger.Log("广告显示成功");
                },
                err => {
                    GameLogger.Error($"广告显示失败: {err.errMsg}");
                    _callback?.Invoke(false, "无适合的广告，广告拉起失败！");
                    _callback = null;
                    _isLoaded = false;
                    _reportSystem.ReportAdvEvent(_consumeType, AdvEvent.FailVideoAdv);
                    UIManager.Instance.HideUIMask();
                    TypeEventSystem.Global.Send<OnAdvCompleteEvent>();
                });

            _callback += (_, __) => UIManager.Instance.HideUIMask();
         
        }
    }
}
#endif
